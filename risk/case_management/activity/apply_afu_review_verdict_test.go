package activity

import (
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	authPb "github.com/epifi/gamma/api/auth"
	afuPb "github.com/epifi/gamma/api/auth/afu"
	caseManagementActivityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	enumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	"github.com/epifi/be-common/pkg/epifitemporal"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	"google.golang.org/protobuf/proto"
)

func TestProcessor_ApplyAfuReviewVerdictStatus(t *testing.T) {
	type args struct {
		req *caseManagementActivityPb.ApplyAFUReviewVerdictRequest
	}
	tests := []struct {
		name      string
		mocks     func(mock *mockedDependencies, args args)
		args      args
		want      *caseManagementActivityPb.ApplyAFUReviewVerdictResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "Failed with Afu attempt not found",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(gomock.Any(), &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: "actorId", Count: 1}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			args: args{
				req: &caseManagementActivityPb.ApplyAFUReviewVerdictRequest{CaseId: "caseId", ActorId: "actorId", AfuId: "afuId", Verdict: enumsPb.Verdict_VERDICT_FAIL},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "Failed with invalid argument in fetch afu attempts",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(gomock.Any(), &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: "actorId", Count: 1}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{Status: rpc.StatusInvalidArgument()}, nil)
			},
			args: args{
				req: &caseManagementActivityPb.ApplyAFUReviewVerdictRequest{CaseId: "caseId", ActorId: "actorId", AfuId: "afuId", Verdict: enumsPb.Verdict_VERDICT_FAIL},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "Failed to fetch afu attempt, retryable error",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(gomock.Any(), &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: "actorId", Count: 1}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{Status: rpc.StatusInternal()}, nil)
			},
			args: args{
				req: &caseManagementActivityPb.ApplyAFUReviewVerdictRequest{CaseId: "caseId", ActorId: "actorId", AfuId: "afuId", Verdict: enumsPb.Verdict_VERDICT_FAIL},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "Success, Input attempt is not latest attempt",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(gomock.Any(), &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: "actorId", Count: 1}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{AuthFactorUpdates: []*afuPb.AuthFactorUpdate{{Id: "differentAfuId"}}, Status: rpc.StatusOk()}, nil)
			},
			args: args{
				req: &caseManagementActivityPb.ApplyAFUReviewVerdictRequest{CaseId: "caseId", ActorId: "actorId", AfuId: "afuId", Verdict: enumsPb.Verdict_VERDICT_FAIL},
			},
			want:    &caseManagementActivityPb.ApplyAFUReviewVerdictResponse{},
			wantErr: false,
		},
		{
			name: "Failed with not found in applying verdict",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(gomock.Any(), &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: "actorId", Count: 1}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{AuthFactorUpdates: []*afuPb.AuthFactorUpdate{{Id: "afuId"}}, Status: rpc.StatusOk()}, nil)
				mock.mockAuthClient.EXPECT().ProcessAFURiskVerdict(gomock.Any(), &authPb.ProcessAFURiskVerdictRequest{
					AfuId:   "afuId",
					ActorId: "actorId",
					CaseId:  "caseId",
					Verdict: authPb.ProcessAFURiskVerdictRequest_VERDICT_FAIL,
				}).Return(&authPb.ProcessAFURiskVerdictResponse{Status: rpc.StatusRecordNotFound()}, nil)
			},
			args: args{
				req: &caseManagementActivityPb.ApplyAFUReviewVerdictRequest{CaseId: "caseId", ActorId: "actorId", AfuId: "afuId", Verdict: enumsPb.Verdict_VERDICT_FAIL},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "Failed with Invalid argument in applying verdict",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(gomock.Any(), &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: "actorId", Count: 1}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{AuthFactorUpdates: []*afuPb.AuthFactorUpdate{{Id: "afuId"}}, Status: rpc.StatusOk()}, nil)
				mock.mockAuthClient.EXPECT().ProcessAFURiskVerdict(gomock.Any(), &authPb.ProcessAFURiskVerdictRequest{
					AfuId:   "afuId",
					ActorId: "actorId",
					CaseId:  "caseId",
					Verdict: authPb.ProcessAFURiskVerdictRequest_VERDICT_FAIL,
				}).Return(&authPb.ProcessAFURiskVerdictResponse{Status: rpc.StatusInvalidArgument()}, nil)
			},
			args: args{
				req: &caseManagementActivityPb.ApplyAFUReviewVerdictRequest{CaseId: "caseId", ActorId: "actorId", AfuId: "afuId", Verdict: enumsPb.Verdict_VERDICT_FAIL},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "Failed with transient error in applying verdict",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(gomock.Any(), &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: "actorId", Count: 1}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{AuthFactorUpdates: []*afuPb.AuthFactorUpdate{{Id: "afuId"}}, Status: rpc.StatusOk()}, nil)
				mock.mockAuthClient.EXPECT().ProcessAFURiskVerdict(gomock.Any(), &authPb.ProcessAFURiskVerdictRequest{
					AfuId:   "afuId",
					ActorId: "actorId",
					CaseId:  "caseId",
					Verdict: authPb.ProcessAFURiskVerdictRequest_VERDICT_FAIL,
				}).Return(&authPb.ProcessAFURiskVerdictResponse{Status: rpc.StatusInternal()}, nil)
			},
			args: args{
				req: &caseManagementActivityPb.ApplyAFUReviewVerdictRequest{CaseId: "caseId", ActorId: "actorId", AfuId: "afuId", Verdict: enumsPb.Verdict_VERDICT_FAIL},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "Success, Verdict Fail",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(gomock.Any(), &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: "actorId", Count: 1}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{AuthFactorUpdates: []*afuPb.AuthFactorUpdate{{Id: "afuId"}}, Status: rpc.StatusOk()}, nil)
				mock.mockAuthClient.EXPECT().ProcessAFURiskVerdict(gomock.Any(), &authPb.ProcessAFURiskVerdictRequest{
					AfuId:   "afuId",
					ActorId: "actorId",
					CaseId:  "caseId",
					Verdict: authPb.ProcessAFURiskVerdictRequest_VERDICT_FAIL,
				}).Return(&authPb.ProcessAFURiskVerdictResponse{Status: rpc.StatusOk()}, nil)
			},
			args: args{
				req: &caseManagementActivityPb.ApplyAFUReviewVerdictRequest{CaseId: "caseId", ActorId: "actorId", AfuId: "afuId", Verdict: enumsPb.Verdict_VERDICT_FAIL},
			},
			want:    &caseManagementActivityPb.ApplyAFUReviewVerdictResponse{},
			wantErr: false,
		},
		{
			name: "Success, Verdict Pass",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockAuthClient.EXPECT().GetAuthFactorUpdatesForActor(gomock.Any(), &authPb.GetAuthFactorUpdatesForActorRequest{ActorId: "actorId", Count: 1}).
					Return(&authPb.GetAuthFactorUpdatesForActorResponse{AuthFactorUpdates: []*afuPb.AuthFactorUpdate{{Id: "afuId"}}, Status: rpc.StatusOk()}, nil)
				mock.mockAuthClient.EXPECT().ProcessAFURiskVerdict(gomock.Any(), &authPb.ProcessAFURiskVerdictRequest{
					AfuId:   "afuId",
					ActorId: "actorId",
					CaseId:  "caseId",
					Verdict: authPb.ProcessAFURiskVerdictRequest_VERDICT_PASS,
				}).Return(&authPb.ProcessAFURiskVerdictResponse{Status: rpc.StatusOk()}, nil)
			},
			args: args{
				req: &caseManagementActivityPb.ApplyAFUReviewVerdictRequest{CaseId: "caseId", ActorId: "actorId", AfuId: "afuId", Verdict: enumsPb.Verdict_VERDICT_PASS},
			},
			want:    &caseManagementActivityPb.ApplyAFUReviewVerdictResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			proc, mockedDeps, assertTest := newProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(proc)
			if tt.mocks != nil {
				tt.mocks(mockedDeps, tt.args)
			}
			got := &caseManagementActivityPb.ApplyAFUReviewVerdictResponse{}
			result, err := env.ExecuteActivity(riskNs.ApplyAFUReviewVerdict, tt.args.req)
			if result != nil && result.HasValue() {
				getErr := result.Get(&got)
				if getErr != nil {
					t.Errorf("ApplyAFUReviewVerdict() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ApplyAFUReviewVerdict() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ApplyAFUReviewVerdict() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(got, tt.want):
				t.Errorf("ApplyAFUReviewVerdict() got = %v, want %v", got, tt.want)
			}
		})
	}
}
