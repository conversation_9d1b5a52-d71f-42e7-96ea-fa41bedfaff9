package cc_securitygroups_deploy

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var internalLbAccessSgRules = &securitygroups.SGIR{
	Name:        "internal-lb-access-sg",
	Account:     cfg.Deploy,
	VpcId:       "vpc-051f4d0205a0dc304",
	Description: "Allow internal ALBs to connect to targets",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "Allow from deploy-internal-infra-alb",
			Protocol:    "tcp",
			FromPort:    0,
			ToPort:      65535,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.Deploy, SecurityGroupName: "deploy-internal-infra-alb", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{},
}
