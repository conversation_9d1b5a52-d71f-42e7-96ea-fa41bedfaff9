package cc_securitygroups_data_dev

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var vpcEndpointsSgRules = &securitygroups.SGIR{
	Name:        "vpc-endpoints-sg",
	Account:     cfg.DataDev,
	VpcId:       "vpc-0b42b2a1aeedb016b",
	Description: "Managed by Terraform",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{},
	Tags:        map[string]string{},
}
