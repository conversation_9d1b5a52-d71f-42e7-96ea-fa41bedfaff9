package cc_securitygroups_uat

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var k8sUat66f5d2d088Rules = &securitygroups.SGIR{
	Name:        "k8s-uat-66f5d2d088",
	Account:     cfg.UAT,
	VpcId:       "vpc-00f6a27e6cf6ee8ff",
	Description: "[k8s] Managed SecurityGroup for LoadBalancer",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    80,
			ToPort:      80,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{"elbv2.k8s.aws/cluster": "epifi-uat",
		"ingress.k8s.aws/resource": "ManagedLBSecurityGroup",
		"ingress.k8s.aws/stack":    "uat",
	},
}
