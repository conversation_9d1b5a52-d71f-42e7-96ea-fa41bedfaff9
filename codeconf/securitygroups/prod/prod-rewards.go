package cc_securitygroups_prod

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var prodRewardsRules = &securitygroups.SGIR{
	Name:        "prod-rewards",
	Account:     cfg.Prod,
	VpcId:       "vpc-0d32af4daaaee9697",
	Description: "Security Group for rewards Service",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from <PERSON>",
			Protocol:    "udp",
			FromPort:    8300,
			ToPort:      8301,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Prometheus for application metrics",
			Protocol:    "tcp",
			FromPort:    9999,
			ToPort:      9999,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "rewards HTTPs Port",
			Protocol:    "tcp",
			FromPort:    9792,
			ToPort:      9792,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "rewards Service HTTP Port",
			Protocol:    "tcp",
			FromPort:    9091,
			ToPort:      9091,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Prometheus for fluentd metrics",
			Protocol:    "tcp",
			FromPort:    24231,
			ToPort:      24231,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "Allow inbound from own security group",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.Prod, SecurityGroupName: "prod-rewards", PrefixList: ""},
		},

		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "allow icmp from security scanning servers",
			Protocol:    "icmp",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.Prod, SecurityGroupName: "icmp-sg", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Prometheus node exporter",
			Protocol:    "tcp",
			FromPort:    9100,
			ToPort:      9100,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "rewards Service Secure Port",
			Protocol:    "tcp",
			FromPort:    9515,
			ToPort:      9515,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Consul",
			Protocol:    "tcp",
			FromPort:    8300,
			ToPort:      8301,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to wazuh(security)",
			Protocol:    "udp",
			FromPort:    1514,
			ToPort:      1515,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to rudder(data-prod)",
			Protocol:    "tcp",
			FromPort:    7001,
			ToPort:      7001,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to CRDB",
			Protocol:    "tcp",
			FromPort:    26257,
			ToPort:      26257,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "outbound to kafka",
			Protocol:    "tcp",
			FromPort:    9094,
			ToPort:      9094,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "prefixlist",
			Egress:      true,
			Description: "Outbound to Prefix lists",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: "pl-66a7420f"},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Fluentd",
			Protocol:    "tcp",
			FromPort:    24224,
			ToPort:      24224,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Presto",
			Protocol:    "tcp",
			FromPort:    8889,
			ToPort:      8889,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Postgres",
			Protocol:    "tcp",
			FromPort:    5432,
			ToPort:      5432,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to wazuh(security)",
			Protocol:    "tcp",
			FromPort:    1514,
			ToPort:      1515,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Consul",
			Protocol:    "udp",
			FromPort:    8300,
			ToPort:      8301,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "outbound to Redis Cloud",
			Protocol:    "tcp",
			FromPort:    10653,
			ToPort:      10653,
			SourceDest:  &securitygroups.SrvObject{Cidr: "***********/24", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "rewards Service HTTP Port",
			Protocol:    "tcp",
			FromPort:    0,
			ToPort:      65535,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to owlh node",
			Protocol:    "tcp",
			FromPort:    50010,
			ToPort:      50010,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Redis",
			Protocol:    "tcp",
			FromPort:    12231,
			ToPort:      12231,
			SourceDest:  &securitygroups.SrvObject{Cidr: "***********/24", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "prefixlist",
			Egress:      true,
			Description: "Outbound to Prefix lists",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: "pl-78a54011"},
		},
	},
	Tags: map[string]string{"Name": "prod-rewards",
		"TFModVersion": "V3",
	},
}
