import os
from opensearchpy import OpenSearch, RequestsHttpConnection
from datetime import date,datetime,timezone
import boto3
from requests_aws4auth import AWS4Auth

APPS = (
    "atlas",
    "auth",
    "card",
    "casbin",
    "comms",
    "connectedaccount",
    "cx",
    "fittt",
    "frontend",
    "insights",
    "kyc",
    "order",
    "paymentinstrument",
    "rewards",
    "savings",
    "search",
    "timeline",
    "tokenizer",
    "user",
    "vendorgateway",
    "vendormapping",
    "vendornotification",
    "vnotificationgw",
    "waitlist",
)


def _get_url(env):
    if env == "secure":
        return "prod-secure-logs.pointz.in"
    if env == "prod":
        return "prod-logs.pointz.in"
    return "logs.pointz.in"


def app_log_count(client, apps, env):
    """
    Runs bucket filters aggregation query to get the count of logs for all apps for the last hour.
    """
    search_filters = {app: {"match": {"@log_name": f"{env}.{app}"}} for app in apps}
    search_body = {
        "query": {
            "bool": {
                "must": [
                    {"range": {"@timestamp": {"gte": "now-5m", "lt": "now"}}},
                ]
            }
        },
        "aggs": {"@log_name": {"filters": {"filters": search_filters}}},
        "size": 0,
    }
    today = datetime.now(timezone.utc).strftime("%Y.%m.%d")
    index_name = f"logstash-go-{today}"
    # print(f"-----------------\n{search_body}\n---------------\n")
    response = client.search(index=index_name, body=search_body)
    print(f"-----------------\n{response}\n---------------\n")
    return response["aggregations"]["@log_name"]["buckets"]

def default_search(index_name):
    client.search(index="logstash-2022.12.*", body={
        "query":
        })
    return


def create_msg(missing_logs, env):
    msg = f"logs missing for following apps in {env} env:"
    for mlogs in missing_logs:
        msg = msg + f"\n{mlogs}|{missing_logs[mlogs]}"
    return msg

def main(region, env):
    credentials = boto3.Session().get_credentials()
    awsauth = AWS4Auth(
        credentials.access_key,
        credentials.secret_key,
        region,
        "es",
        session_token=credentials.token,
    )
    host_url = _get_url(env)
    client = OpenSearch(
        hosts=[{"host": host_url, "port": "443"}],
        enable_compress=True,
        http_auth=awsauth,
        use_ssl=True,
        verify_certs=True,
        connection_class=RequestsHttpConnection,
    )
    response = default_search(index_name)
    print(response)

if __name__ == "__main__":
    region = "ap-south-1"
    env = "secure"
    main(region, env)
