import logging
import sys

from jinja2 import Environment, FileSystemLoader
import scripts.utils.commons as common
import os
from airflow.models import Variable

TEMPLATE_DIR = os.path.join(os.path.dirname(__file__), '../../templates')
DAG_DIR = os.path.join(os.path.dirname(__file__), '../../dags')


def generate_sqoop_dag(**kwargs):
    run_for = kwargs['dag_run'].conf['usecase']
    if run_for == 'epifi':
        generate_sqoop_dags_by_size(**kwargs)
        return
    sqoop_dag_config = Variable.get("sqoop_dag_config", deserialize_json=True)
    sqoop_slas = Variable.get("sqoop_slas", deserialize_json=True)
    if run_for =='all':
        usecases = sqoop_dag_config.keys()
    else :
        usecases = list()
        usecases.append(run_for)

    for usecase in usecases:
        if usecase == 'epifi':
            logging.info('Disabled for epifi!! Please use generate_sqoop_dags_by_size!')
            sys.exit(1)
        crontime = sqoop_dag_config[usecase]['crontime']
        concurrency = sqoop_dag_config[usecase]['concurrency']
        usecase_conf = common.get_sqoop_config(usecase)
        tool = 'sqoop'
        if 'tool' in usecase_conf:
            tool = usecase_conf['tool']

        jinja_environment = Environment(autoescape=False, loader=FileSystemLoader(TEMPLATE_DIR))
        template = jinja_environment.get_template('sqoop_dag_template.py')
        template.stream(usecase=usecase,crontime=crontime, tables=usecase_conf['tables'],tool=tool, concurrency=concurrency, slas=sqoop_slas).dump('{dag_dir}/sqoop_{usecase}.py'
                                                                                               .format(dag_dir=DAG_DIR,
                                                                                                       usecase=usecase))


def generate_sqoop_dags_by_size(**kwargs):
    run_for = kwargs['dag_run'].conf['usecase']
    sqoop_dag_config = Variable.get("sqoop_dag_config", deserialize_json=True)
    sqoop_slas = Variable.get("sqoop_slas", deserialize_json=True)
    if run_for == 'all':
        usecases = sqoop_dag_config.keys()
    else:
        usecases = list()
        usecases.append(run_for)

    for usecase in usecases:
        crontime = sqoop_dag_config[usecase]['crontime']
        concurrency = sqoop_dag_config['concurrency']
        usecase_conf = common.get_sqoop_config(usecase)

        sizes = ['small', 'medium', 'large']
        for size in sizes:
            jinja_environment = Environment(autoescape=False, loader=FileSystemLoader(TEMPLATE_DIR))
            template = jinja_environment.get_template('sqoop_dag_by_size_template.py')
            template.stream(usecase=usecase, crontime=crontime, tables=usecase_conf['tables'], concurrency=concurrency,
                        slas=sqoop_slas, size=size).dump('{dag_dir}/sqoop_{usecase}_{size}.py'
                                              .format(
                                                        dag_dir=DAG_DIR,
                                                        usecase=usecase,
                                                        size=size
                                                     )
                                              )