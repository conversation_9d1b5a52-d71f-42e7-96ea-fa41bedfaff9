#! /usr/bin/env python3
import sys
import boto3
import random
app = sys.argv[1]
env = sys.argv[2]
REGION = 'ap-south-1'


ROLE_ARNS = {
    'deploy': 'arn:aws:iam::632884248997:role/',
    'uat': 'arn:aws:iam::406142726310:role/',
    'qa': 'arn:aws:iam::366873573630:role/',
    'demo': 'arn:aws:iam::571894593668:role/',
    'staging': 'arn:aws:iam::785240676363:role/',
    'prod': 'arn:aws:iam::854002675954:role/'
}

def get_aws_creds(env):
    ARN_ROLE_SESSION_NAME = "Jenkins-CheckClean-"
    deploy_role_arn = ROLE_ARNS[env] + 'role_decrypt_deploy'
    client = boto3.client('sts')
    assume_role_object = client.assume_role(DurationSeconds=900, RoleArn=deploy_role_arn,
        RoleSessionName=ARN_ROLE_SESSION_NAME + env + str(random.randint(1000, 9999)),)
    credentials = assume_role_object['Credentials']
    return credentials


def get_assumed_role_boto_client(aws_service, env):
    if env == 'prod':
        client = boto3.client(aws_service, REGION)
    else:
        credentials = get_aws_creds(env)
        client = boto3.client(aws_service, REGION, aws_access_key_id=credentials['AccessKeyId'],
                              aws_secret_access_key=credentials['SecretAccessKey'],
                              aws_session_token=credentials['SessionToken'])
    return client


def is_passive_listener(app, env):
    try:
        tg_client = get_assumed_role_boto_client('elbv2', env)
        LoadBalancerArn = tg_client.describe_load_balancers(Names=[env + '-' + app + '-nlb'])['LoadBalancers'][0]['LoadBalancerArn']
        LBListeners = tg_client.describe_listeners(LoadBalancerArn=LoadBalancerArn)['Listeners']
        for listener in LBListeners:
            if listener['Port'] == 4442:
                return True
    except:
        return False
    return False


print(is_passive_listener(app, env))
