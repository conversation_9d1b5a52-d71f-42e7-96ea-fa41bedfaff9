pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '10'))
    }
    parameters {
        string(name: 'fromTime', description: 'from time in unix format')
        string(name: 'toTime', description: 'to time in unix format')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkins<PERSON>le.')
    }
    environment {
        ENV="${params.Env}"
        TARGET="onboarding_god_script"
        VERSION="4.1.18"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Fetch Binary') {
            steps {
                dir('app'){
                    script{
                    sh "aws s3 cp s3://epifi-gamma-binaries-prod/${TARGET}/linux/amd64/${VERSION} ./${TARGET}.zip"
                    sh "unzip -o ${TARGET}.zip"
                    }
                }
            }
        }
        stage('Parge Build Args') {
            steps {
                script {
                    def buildArgs = [:]

                    // Loop through all environment variables
                    params.keySet().findAll { it.startsWith("BUILD_ARG_") }.each { key ->
                        // Remove the prefix and store the value with key in buildArgs map
                        buildArgs[key - "BUILD_ARG_"] = env[key]
                    }

                    // Construct the command with proper argument values
                    env.command = ""
                    buildArgs.each { k, v ->
                        // Enclose the value in quotes only if it contains spaces
                        def argValue = (v.contains(" ")) ? "\'${v}\'" : v
                        env.command += "-${k}=${argValue} "
                    }
                    echo env.command.trim()
                }
            }
        }
        stage("Execute Binary") {
            steps {
                dir('app') {
                    script {
                        sh """
                            cd ${TARGET}
                            chmod 700 ${TARGET}_bin
                            ENVIRONMENT=prod DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true ./${TARGET}_bin -JobName=JOB_MAIL_SA_CLOSURE_REQUESTS -Args1=${fromTime} -Args2=${toTime}
                        """
                    }
                }
            }
        }
    }

    post {
        failure {
            slackSend channel: "<!subteam^S05UWQU2HGA|@cg-oncall>", color: "#8B0000", failOnError:true, message:"<@S05UWQU2HGA> <@U0410M4D3TM> <@U05781B95BL> Build failed  - ${env.JOB_NAME} ${env.BUILD_NUMBER} (<${env.BUILD_URL}|View Logs>)"
        }
    }
}
