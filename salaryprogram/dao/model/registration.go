package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/nulltypes"

	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
)

// SalaryProgramRegistration stores the registration of a user into the salary program.
type SalaryProgramRegistration struct {
	Id string `gorm:"primaryKey"`

	// actor whose account is getting registered for the salary program.
	ActorId nulltypes.NullString

	// 1. internal id of the account which is getting registered for the salary program for salary or salary lite flow
	// 2a. Externally connected account id in case of AA salary flow and income estimated via AA flow
	// 2b. "sms-parser-v%d" in case of income estimated via SMS flow
	AccountId nulltypes.NullString

	// version of registration flow used for registering for the salary program.
	RegistrationFlowVersion salaryprogramPb.SalaryProgramRegistrationFlowVersion

	// time at which salary program registration was completed,
	// will be NULL if registration is not completed
	CompletedAt nulltypes.NullTime

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt

	// AccountType indicates the type of account for which the salary program flows registration was done
	AccountType salaryprogramPb.SalaryProgramRegistrationAccountType

	// RegistrationFlowType indicates the flow type for which the registration was done.
	RegistrationFlowType salaryprogramPb.SalaryProgramRegistrationFlowType
}

func NewRegistration(id string, reg *salaryprogramPb.SalaryProgramRegistration) *SalaryProgramRegistration {
	return &SalaryProgramRegistration{
		Id:                      id,
		ActorId:                 nulltypes.NewNullString(reg.GetActorId()),
		AccountId:               nulltypes.NewNullString(reg.GetAccountId()),
		RegistrationFlowVersion: reg.GetRegistrationFlowVersion(),
		CompletedAt:             nulltypes.NewNullTime(dateTimePkg.TimestampToTime(reg.GetCompletedAt())),
		AccountType:             reg.GetAccountType(),
		RegistrationFlowType:    reg.GetRegistrationFlowType(),
	}
}

func (reg *SalaryProgramRegistration) GetProto() *salaryprogramPb.SalaryProgramRegistration {
	regProto := &salaryprogramPb.SalaryProgramRegistration{
		Id:                      reg.Id,
		ActorId:                 reg.ActorId.GetValue(),
		AccountId:               reg.AccountId.GetValue(),
		RegistrationFlowVersion: reg.RegistrationFlowVersion,
		CreatedAt:               timestampPb.New(reg.CreatedAt),
		UpdatedAt:               timestampPb.New(reg.UpdatedAt),
		CompletedAt:             reg.CompletedAt.GetProto(),
		AccountType:             reg.AccountType,
		RegistrationFlowType:    reg.RegistrationFlowType,
	}

	if reg.DeletedAt.Valid {
		regProto.DeletedAt = timestampPb.New(reg.DeletedAt.Time)
	}

	return regProto
}
