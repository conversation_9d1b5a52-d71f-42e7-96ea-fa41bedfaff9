// nolint:dupl
package scrollable

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	deFePb "github.com/epifi/gamma/api/frontend/dynamic_elements"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	homeFePb "github.com/epifi/gamma/api/frontend/home"
	homeOrchFePb "github.com/epifi/gamma/api/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>/cache"
	"github.com/epifi/gamma/frontend/home/<USER>/metrics"
)

type TabbedCardComponent struct {
	deFeClient deFePb.DynamicElementsClient
}

func NewTabbedCardComponent(deFeClient deFePb.DynamicElementsClient) *TabbedCardComponent {
	return &TabbedCardComponent{
		deFeClient: deFeClient,
	}
}

func (t *TabbedCardComponent) GetScrollableComponent(ctx context.Context, request *headerPb.RequestHeader, requestMetadata *homeOrchFePb.HomePageRequestMetadata, widgetLayout *homeFePb.HomeWidget, componentCacheStore *cache.ComponentCacheStore, subComponentId string) (*homeOrchFePb.ScrollableComponent, error) {
	if request == nil || widgetLayout == nil {
		return nil, fmt.Errorf("mandatory parameters request or layout are missing")
	}

	dynamicElementsRes, err := t.deFeClient.FetchDynamicElements(ctx, &deFePb.FetchDynamicElementsRequest{
		Req:     request,
		ActorId: request.GetAuth().GetActorId(),
		ClientContext: &deFePb.ClientContext{
			ScreenName: deeplinkPb.Screen_HOME,
			ScreenAdditionalInfo: &deFePb.ClientContext_HomeInfo{
				HomeInfo: &deFePb.HomeScreenAdditionalInfo{
					Section: deFePb.HomeScreenAdditionalInfo_SECTION_TABBED_CARD,
					Version: deFePb.HomeScreenAdditionalInfo_VERSION_V2,
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(dynamicElementsRes.GetRespHeader(), err); rpcErr != nil {
		if dynamicElementsRes.GetRespHeader().GetStatus().IsRecordNotFound() {
			return nil, nil
		}
		metrics.Recorder.RecordComponentGenerationError(constants.TabbedCardScrollableComponentId.String(), "", rpcErr)
		logger.Error(ctx, "error while fetching tabbed card widget", zap.Error(rpcErr))
		return &homeOrchFePb.ScrollableComponent{
			Id:                constants.TabbedCardScrollableComponentId.String(),
			WidgetType:        homeFePb.HomeWidget_WIDGET_TYPE_TABBED_CARD,
			WidgetBg:          widgetLayout.GetWidgetBg(),
			WidgetBgSeparator: widgetLayout.GetWidgetBgSeparator(),
			TopSpacer:         widgetLayout.GetTopSpacer(),
			BottomSpacer:      widgetLayout.GetBottomSpacer(),
			RetryStrategy: &homeOrchFePb.RetryStrategy{
				RetryType: homeOrchFePb.RetryType_RETRY_TYPE_CLIENT_INITIATED,
			},
		}, nil
	}

	metrics.Recorder.RecordComponentGenerationSuccess(constants.TabbedCardScrollableComponentId.String(), "")
	return &homeOrchFePb.ScrollableComponent{
		Id:                constants.TabbedCardScrollableComponentId.String(),
		WidgetType:        homeFePb.HomeWidget_WIDGET_TYPE_TABBED_CARD,
		WidgetBg:          widgetLayout.GetWidgetBg(),
		WidgetBgSeparator: widgetLayout.GetWidgetBgSeparator(),
		TopSpacer:         widgetLayout.GetTopSpacer(),
		BottomSpacer:      widgetLayout.GetBottomSpacer(),
		Component: &homeOrchFePb.ScrollableComponent_DynamicElementsComponent{
			DynamicElementsComponent: dynamicElementsRes,
		},
	}, nil
}
