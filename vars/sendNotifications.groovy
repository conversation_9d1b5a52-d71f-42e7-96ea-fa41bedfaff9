import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

def call(Map config = [:]) {
  String channel = config.channelName
  String buildResult=currentBuild.currentResult
  String ENV=config.ENV
  String jobDetails=config.jobDetails

  def colorName = 'RED'
  def colorCode = '#FF0000'
  def titleName ="*${env.JOB_NAME} ${buildResult} [<${env.BUILD_URL}|${env.BUILD_NUMBER}>]*\n" + "[<${env.RUN_DISPLAY_URL}| Pipeline>] [<${env.RUN_CHANGES_DISPLAY_URL}|  Changes>]"

  if (buildResult == 'STARTED') {
    color = 'YELLOW'
    colorCode = '#FFFF00'
  } else if (buildResult == 'SUCCESSFUL') {
    color = 'GREEN'
    colorCode = 'good'
  } else if (buildResult == 'ABORTED') {
    color = '#808080'
    colorCode = 'Gray'
  } else {
    color = 'RED'
    colorCode = 'danger'
  }
  
  // JSONObject for tilename
  JSONObject attachment = new JSONObject();
  attachment.put('author',"jenkins");
  attachment.put('text', titleName.toString());
  //attachment.put('fallback', "${env.JOB_NAME} ${buildResult}");
  attachment.put('color',colorCode);
  attachment.put('mrkdwn_in', ["fields"])
  // JSONObject for ENV
  JSONObject environment = new JSONObject();
  environment.put('title', 'Env');
  environment.put('value', ENV.toString());
  environment.put('short', false);
  // JSONObject for Job Details
  JSONObject comments = new JSONObject();
  comments.put('title', 'Details');
  comments.put('value', jobDetails.toString());
  comments.put('short', false);
  attachment.put('fields', [environment, comments]);
  JSONArray attachments = new JSONArray();
  attachments.add(attachment);
  //println attachments.toString()
  // Send notifications
  slackSend (color: colorCode, attachments: attachments.toString(), channel: channel)

}