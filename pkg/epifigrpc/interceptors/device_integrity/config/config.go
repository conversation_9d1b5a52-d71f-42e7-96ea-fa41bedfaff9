package config

import (
	"time"

	"github.com/epifi/be-common/pkg/frontend/app"
)

//go:generate conf_gen github.com/epifi/be-common/pkg/epifigrpc/interceptors/device_integrity/config DeviceIntegrityConfig
type DeviceIntegrityConfig struct {
	// not adding different separate whitelisting config for iOS as this is a temp change and will be removed later
	EnableWhitelistedTokens bool
	// a kill switch to skip all async device integrity checks during a contingency like google outage, or limits breach
	SkipAsyncDeviceIntegrityChecks        bool `dynamic:"true"`
	WhitelistedTokensList                 []string
	DefaultHighRiskDeviceConsentDuration  time.Duration
	MaxHighRiskDeviceConsentDuration      time.Duration
	AsyncDeviceIntegrityCheck             *app.FeatureConfig `dynamic:"true"`
	AsyncDeviceIntegrityRolloutPercentage int                `dynamic:"true"`
	// device integrity gets bypassed for these phone numbers
	BypassPhoneNumbers []string `dynamic:"true"`
}
