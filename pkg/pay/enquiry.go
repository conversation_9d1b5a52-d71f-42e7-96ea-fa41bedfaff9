package pay

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/epifigrpc"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
)

type PaymentEnquiryParams struct {
	NotFoundMaxRetryDurationVendorMap map[string]*notFoundMaxRetryDuration
	InProgressToSuccessMap            map[string]*inProgressToSuccess
	PaymentProcessingSLAVendorMap     map[string]*paymentProcessingSLA
}

type paymentProtocolToDeemedEnquiryDurationMap struct {
	// time duration till which enquiry needs to be done for deemed p2p upi transactions
	P2P time.Duration
	// time duration till which enquiry needs to be done for deemed p2m upi transactions
	P2M time.Duration
}

// notFoundMaxRetryDuration defines the max duration for which application should retry payment enquiry
// in case of vendor returns NOT FOUND response.
type notFoundMaxRetryDuration struct {
	IntraBank time.Duration
	UPI       time.Duration
	NEFT      time.Duration
	RTGS      time.Duration
	IMPS      time.Duration
}

// inProgressToSuccess defines max duration for which payment should move in-progress transaction
// with specific error codes to success transaction.
type inProgressToSuccess struct {
	FiErrorCodes                              []string
	PaymentProtocolToDurationMap              map[string]time.Duration
	PaymentProtocolToDeemedEnquiryDurationMap map[string]*paymentProtocolToDeemedEnquiryDurationMap
	// To be used when off-app txns (i.e. received via statement or notification-callback) are returning DE upon enquiry
	OffAppPaymentProtocolToDeadlineExceededDuration map[string]time.Duration
	// To be used when off-app txns are returning NF upon enquiry
	OffAppPaymentProtocolToNotFoundDuration map[string]time.Duration
}

// ShouldRetryForRecordNotFound returns true if record not found should be treated as transient failure
func ShouldRetryForRecordNotFound(paymentEnquiryParams *PaymentEnquiryParams, txn *paymentPb.Transaction) bool {
	notFoundMaxRetryDuration, ok := paymentEnquiryParams.NotFoundMaxRetryDurationVendorMap[txn.GetPartnerBank().String()]
	if !ok {
		return false
	}

	var maxDuration time.Duration
	switch txn.GetPaymentProtocol() {
	case paymentPb.PaymentProtocol_NEFT:
		maxDuration = notFoundMaxRetryDuration.NEFT
	case paymentPb.PaymentProtocol_IMPS:
		maxDuration = notFoundMaxRetryDuration.IMPS
	case paymentPb.PaymentProtocol_RTGS:
		maxDuration = notFoundMaxRetryDuration.RTGS
	case paymentPb.PaymentProtocol_UPI:
		maxDuration = notFoundMaxRetryDuration.UPI
	case paymentPb.PaymentProtocol_INTRA_BANK:
		maxDuration = notFoundMaxRetryDuration.IntraBank
	default:
		return false
	}

	return time.Since(txn.GetCreatedAt().AsTime()) < maxDuration
}

// ShouldMoveInProgressTransactionToSuccess returns true when fiErrorCode with in progress status return from vendorgateway response should be treated as success.
func ShouldMoveInProgressTransactionToSuccess(paymentEnquiryParams *PaymentEnquiryParams, resFiErrorCode string, txn *paymentPb.Transaction) bool {
	inProgressToSuccess, ok := paymentEnquiryParams.InProgressToSuccessMap[txn.GetPartnerBank().String()]
	if !ok {
		return false
	}

	if !lo.Contains(inProgressToSuccess.FiErrorCodes, resFiErrorCode) {
		return false
	}

	var maxDuration time.Duration
	if maxDuration, ok = inProgressToSuccess.PaymentProtocolToDurationMap[txn.GetPaymentProtocol().String()]; !ok {
		return false
	}

	return time.Since(txn.GetCreatedAt().AsTime()) > maxDuration
}

// IsDeemedTransactionEnquiryDurationOver - checks weather the enquiry duration for deemed transaction is over or not
func IsDeemedTransactionEnquiryDurationOver(ctx context.Context, txn *paymentPb.Transaction, piClient piPb.PiClient, paymentEnquiryParams *PaymentEnquiryParams) (bool, error) {
	enquiryDurationConf := paymentEnquiryParams.InProgressToSuccessMap[txn.GetPartnerBank().String()].
		PaymentProtocolToDeemedEnquiryDurationMap[txn.GetPaymentProtocol().String()]

	if txn.GetPaymentProtocol() != paymentPb.PaymentProtocol_UPI {
		return time.Now().After(txn.GetCreatedAt().AsTime().Add(enquiryDurationConf.P2P)), nil
	}

	piResp, err := piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{
		Id: txn.GetPiTo(),
	})
	if err = epifigrpc.RPCError(piResp, err); err != nil {
		return false, fmt.Errorf(
			"failed to get pi response from piId %s, %w", txn.GetPiTo(), err)
	}

	if piResp.GetPaymentInstrument().IsMerchantPI() {
		return time.Now().After(txn.GetCreatedAt().AsTime().Add(enquiryDurationConf.P2M)), nil
	}
	return time.Now().After(txn.GetCreatedAt().AsTime().Add(enquiryDurationConf.P2P)), nil
}

// paymentProcessingSLA define the time at which enquiry(fetch transaction status at vendors end) is made after initiating the transaction
type paymentProcessingSLA struct {
	IntraProcessingTime time.Duration
	NeftProcessingTime  time.Duration
	UpiProcessingTime   time.Duration
	RtgsProcessingTime  time.Duration
	ImpsProcessingTime  time.Duration
}
