package sqs

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/pkg/aws/v2/sqs/mocks"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"
)

func Test_sqsService_AddToQueue(t *testing.T) {
	t.<PERSON>llel()
	type fields struct {
		sqsClient           SQSAPI
		queueName           string
		queueURL            string
		queueOwnerAccountId string
	}
	type args struct {
		ctx          context.Context
		message      interface{}
		delaySeconds *int32
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		setupMockCalls func(mockSqsClient *mocks.MockSQSAPI)
		want           string
		wantErr        bool
	}{
		{
			name:    "unexpected message type",
			args:    args{message: "wrong input"},
			want:    "",
			wantErr: true,
		},
		{
			name: "ErrCodeInvalidMessageContents error",
			fields: fields{
				queueName: queueName,
				queueURL:  "queue-url",
			},
			args: args{
				ctx:          context.Background(),
				message:      &sqs.SendMessageInput{},
				delaySeconds: integer.NewInt32(2),
			},
			setupMockCalls: func(mockSqsClient *mocks.MockSQSAPI) {
				mockSqsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).Return(&sqs.SendMessageOutput{}, errors.New(ErrCodeInvalidMessageContents)).Times(1)
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "other errors",
			fields: fields{
				queueName: queueName,
				queueURL:  "queue-url",
			},
			args: args{
				ctx:          context.Background(),
				message:      &sqs.SendMessageInput{},
				delaySeconds: integer.NewInt32(2),
			},
			setupMockCalls: func(mockSqsClient *mocks.MockSQSAPI) {
				mockSqsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).Return(&sqs.SendMessageOutput{}, errors.New("test error")).Times(1)
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "Add to queue successful",
			fields: fields{
				queueName: queueName,
				queueURL:  "queue-url",
			},
			args: args{
				ctx:          context.Background(),
				message:      &sqs.SendMessageInput{},
				delaySeconds: integer.NewInt32(2),
			},
			setupMockCalls: func(mockSqsClient *mocks.MockSQSAPI) {
				mockSqsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).Return(&sqs.SendMessageOutput{MessageId: aws.String("message-id")}, nil).Times(1)
			},
			want:    "message-id",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockSqsClient := mocks.NewMockSQSAPI(ctr)
			if tt.setupMockCalls != nil {
				tt.setupMockCalls(mockSqsClient)
			}
			q := &sqsService{
				sqsClient:           mockSqsClient,
				queueName:           tt.fields.queueName,
				queueURL:            tt.fields.queueURL,
				queueOwnerAccountId: tt.fields.queueOwnerAccountId,
			}
			got, err := q.AddToQueue(tt.args.ctx, tt.args.message, tt.args.delaySeconds)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddToQueue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AddToQueue() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_sqsService_DeleteFromQueue(t *testing.T) {
	t.Parallel()
	type fields struct {
		queueName           string
		queueURL            string
		queueOwnerAccountId string
	}
	type args struct {
		ctx     context.Context
		message interface{}
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		setupMockCalls func(mockSqsClient *mocks.MockSQSAPI)
		wantErr        bool
	}{
		{
			name: "unexpected message type",
			args: args{
				message: "wrong input",
			},
			wantErr: true,
		},
		{
			name: "message deletion failed",
			fields: fields{
				queueName: queueName,
				queueURL:  "queue-url",
			},
			args: args{
				message: &types.Message{
					ReceiptHandle: aws.String("receipt-handle"),
				},
			},
			setupMockCalls: func(mockSqsClient *mocks.MockSQSAPI) {
				mockSqsClient.EXPECT().DeleteMessage(gomock.Any(), &sqs.DeleteMessageInput{
					QueueUrl:      aws.String("queue-url"),
					ReceiptHandle: aws.String("receipt-handle"),
				}).Return(&sqs.DeleteMessageOutput{}, errors.New("test error")).Times(1)
			},
			wantErr: true,
		},
		{
			name: "message deletion successful",
			fields: fields{
				queueName: queueName,
				queueURL:  "queue-url",
			},
			args: args{
				message: &types.Message{
					ReceiptHandle: aws.String("receipt-handle"),
				},
			},
			setupMockCalls: func(mockSqsClient *mocks.MockSQSAPI) {
				mockSqsClient.EXPECT().DeleteMessage(gomock.Any(), &sqs.DeleteMessageInput{
					QueueUrl:      aws.String("queue-url"),
					ReceiptHandle: aws.String("receipt-handle"),
				}).Return(&sqs.DeleteMessageOutput{}, nil).Times(1)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockSqsClient := mocks.NewMockSQSAPI(ctr)
			if tt.setupMockCalls != nil {
				tt.setupMockCalls(mockSqsClient)
			}
			q := &sqsService{
				sqsClient:           mockSqsClient,
				queueName:           tt.fields.queueName,
				queueURL:            tt.fields.queueURL,
				queueOwnerAccountId: tt.fields.queueOwnerAccountId,
			}
			if err := q.DeleteFromQueue(tt.args.ctx, tt.args.message); (err != nil) != tt.wantErr {
				t.Errorf("DeleteFromQueue() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_sqsService_ChangeVisibility(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	type fields struct {
		queueName           string
		queueURL            string
		queueOwnerAccountId string
	}
	type args struct {
		ctx      context.Context
		duration int32
		message  interface{}
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		setupMockCalls func(mockSqsClient *mocks.MockSQSAPI)
		wantErr        bool
	}{
		{
			name: "unexpected message type",
			args: args{
				message: "wrong message",
			},
			wantErr: true,
		},
		{
			name: "fail to change message visibility",
			fields: fields{
				queueName: queueName,
				queueURL:  "queue-url",
			},
			args: args{
				duration: int32(5),
				message: &types.Message{
					ReceiptHandle: aws.String("receipt-handle"),
				},
			},
			setupMockCalls: func(mockSqsClient *mocks.MockSQSAPI) {
				mockSqsClient.EXPECT().ChangeMessageVisibility(gomock.Any(), &sqs.ChangeMessageVisibilityInput{
					ReceiptHandle:     aws.String("receipt-handle"),
					QueueUrl:          aws.String("queue-url"),
					VisibilityTimeout: int32(5),
				}).Return(&sqs.ChangeMessageVisibilityOutput{}, errors.New("test error")).Times(1)
			},
			wantErr: true,
		},
		{
			name: "change message visibility successful",
			fields: fields{
				queueName: queueName,
				queueURL:  "queue-url",
			},
			args: args{
				ctx:      context.Background(),
				duration: int32(5),
				message: &types.Message{
					ReceiptHandle: aws.String("receipt-handle"),
				},
			},
			setupMockCalls: func(mockSqsClient *mocks.MockSQSAPI) {
				mockSqsClient.EXPECT().ChangeMessageVisibility(context.Background(), &sqs.ChangeMessageVisibilityInput{
					ReceiptHandle:     aws.String("receipt-handle"),
					QueueUrl:          aws.String("queue-url"),
					VisibilityTimeout: int32(5),
				}).Return(&sqs.ChangeMessageVisibilityOutput{}, nil).Times(1)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockSqsClient := mocks.NewMockSQSAPI(ctr)
			if tt.setupMockCalls != nil {
				tt.setupMockCalls(mockSqsClient)
			}
			q := &sqsService{
				sqsClient:           mockSqsClient,
				queueName:           tt.fields.queueName,
				queueURL:            tt.fields.queueURL,
				queueOwnerAccountId: tt.fields.queueOwnerAccountId,
			}
			if err := q.ChangeVisibility(tt.args.ctx, tt.args.duration, tt.args.message); (err != nil) != tt.wantErr {
				t.Errorf("ChangeVisibility() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_sqsService_ReceiveMessages(t *testing.T) {
	t.Parallel()
	logger.Init("test")
	testRecieveMsgOutput := &sqs.ReceiveMessageOutput{
		Messages: []types.Message{
			{
				Body: aws.String("test-body"),
			},
		},
	}
	type fields struct {
		queueName           string
		queueURL            string
		queueOwnerAccountId string
	}
	type args struct {
		ctx             context.Context
		pollingDuration int32
		maxMessages     int32
	}
	tests := []struct {
		name           string
		fields         fields
		args           args
		setupMockCalls func(mockSqsClient *mocks.MockSQSAPI)
		want           interface{}
		wantErr        bool
	}{
		{
			name: "fail to poll message from queue",
			fields: fields{
				queueName: queueName,
				queueURL:  "queue-url",
			},
			args: args{
				pollingDuration: int32(5),
				maxMessages:     int32(5),
			},
			setupMockCalls: func(mockSqsClient *mocks.MockSQSAPI) {
				mockSqsClient.EXPECT().ReceiveMessage(gomock.Any(), gomock.Any()).Return(nil, errors.New("test error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "poll messages from queue successful",
			fields: fields{
				queueName: queueName,
				queueURL:  "queue-url",
			},
			args: args{
				ctx:             context.Background(),
				pollingDuration: int32(5),
				maxMessages:     int32(5),
			},
			setupMockCalls: func(mockSqsClient *mocks.MockSQSAPI) {
				mockSqsClient.EXPECT().ReceiveMessage(gomock.Any(), gomock.Any()).Return(testRecieveMsgOutput, nil)
			},
			want:    testRecieveMsgOutput,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			mockSqsClient := mocks.NewMockSQSAPI(ctr)
			if tt.setupMockCalls != nil {
				tt.setupMockCalls(mockSqsClient)
			}
			q := &sqsService{
				sqsClient:           mockSqsClient,
				queueName:           tt.fields.queueName,
				queueURL:            tt.fields.queueURL,
				queueOwnerAccountId: tt.fields.queueOwnerAccountId,
			}
			got, err := q.ReceiveMessages(tt.args.ctx, tt.args.pollingDuration, tt.args.maxMessages)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReceiveMessages() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ReceiveMessages() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_sqsService_GetQueueName(t *testing.T) {
	t.Parallel()
	type fields struct {
		sqsClient           SQSAPI
		queueName           string
		queueURL            string
		queueOwnerAccountId string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "happy flow",
			fields: fields{
				queueName: queueName,
			},
			want: queueName,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			q := &sqsService{
				sqsClient:           tt.fields.sqsClient,
				queueName:           tt.fields.queueName,
				queueURL:            tt.fields.queueURL,
				queueOwnerAccountId: tt.fields.queueOwnerAccountId,
			}
			if got := q.GetQueueName(); got != tt.want {
				t.Errorf("GetQueueName() = %v, want %v", got, tt.want)
			}
		})
	}
}
