package fetch_data_processor

import (
	"context"
	"math"
	"reflect"
	"testing"
	"time"

	"github.com/pkg/errors"

	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"

	bankCustMocks "github.com/epifi/gamma/api/bankcust/mocks"
	chatbotWorkflowPb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	vkycMocks "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	mockHelper "github.com/epifi/gamma/cx/test/mocks/helper"

	"github.com/golang/mock/gomock"
)

var (
	userDetailsParams	= &chatbotWorkflowPb.FetchDataParameters{
		EntityParameters: &chatbotWorkflowPb.FetchDataParameters_UserDetailsParameters{
			UserDetailsParameters: &chatbotWorkflowPb.UserDetailsParameters{
				ActorId: "actor-1",
			},
		},
	}

	userDetailsData	= &chatbotWorkflowPb.WorkflowData{
		Data: &chatbotWorkflowPb.WorkflowData_UserDetailsData{
			UserDetailsData: &chatbotWorkflowPb.UserDetailsData{
				VkycStatus:			chatbotWorkflowPb.UserDetailsData_VKYC_STATUS_APPROVED,
				VkycCompletionDeadlineDate:	"23-Dec-2008",
				AccountClosureDate:		"26-Dec-2008",
			},
		},
	}
	userDetailsData2	= &chatbotWorkflowPb.WorkflowData{
		Data: &chatbotWorkflowPb.WorkflowData_UserDetailsData{
			UserDetailsData: &chatbotWorkflowPb.UserDetailsData{
				VkycStatus:			chatbotWorkflowPb.UserDetailsData_VKYC_STATUS_NEVER_ATTEMPTED_FULL_KYC,
				VkycCompletionDeadlineDate:	"23-Feb-2012",
				AccountClosureDate:		"26-Feb-2012",
			},
		},
	}
	userDetailsData3	= &chatbotWorkflowPb.WorkflowData{
		Data: &chatbotWorkflowPb.WorkflowData_UserDetailsData{
			UserDetailsData: &chatbotWorkflowPb.UserDetailsData{
				VkycStatus:			chatbotWorkflowPb.UserDetailsData_VKYC_STATUS_NEVER_ATTEMPTED_MIN_KYC,
				VkycCompletionDeadlineDate:	"23-Dec-2008",
				AccountClosureDate:		"26-Dec-2008",
			},
		},
	}
	userDetailsData4	= &chatbotWorkflowPb.WorkflowData{
		Data: &chatbotWorkflowPb.WorkflowData_UserDetailsData{
			UserDetailsData: &chatbotWorkflowPb.UserDetailsData{
				VkycStatus:			chatbotWorkflowPb.UserDetailsData_VKYC_STATUS_IN_REVIEW,
				VkycCompletionDeadlineDate:	"23-Dec-2008",
				AccountClosureDate:		"26-Dec-2008",
				VkycReviewDurationInDays:	vkycRecordReviewDaysCount,
			},
		},
	}
	accountCreationTime1		= timestamppb.New(time.Unix(**********, 0))
	accountCreationTime2		= timestamppb.New(time.Unix(**********, 0))
	vkycRecordUpdatedAtTime		= timestamppb.New(time.Unix(*********, 0))
	reviewDuration			= time.Now().Sub(vkycRecordUpdatedAtTime.AsTime())
	vkycRecordReviewDaysCount	= int64(math.Ceil(reviewDuration.Hours() / 24))
)

func TestUserDetailsProcessor_FetchData(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockCustomerIdentifier := mockHelper.NewMockICustomerIdentifier(ctr)
	mockVkycClient := vkycMocks.NewMockVKYCClient(ctr)
	mockBankCustomerClient := bankCustMocks.NewMockBankCustomerServiceClient(ctr)
	type args struct {
		mocks		[]interface{}
		ctx		context.Context
		workflowEntity	chatbotWorkflowPb.WorkflowEntity
		params		*chatbotWorkflowPb.FetchDataParameters
	}
	tests := []struct {
		name	string
		args	args
		want	*chatbotWorkflowPb.WorkflowData
		wantErr	bool
	}{
		{
			name:	"invalid arg",
			args: args{
				ctx:		context.Background(),
				workflowEntity:	0,
				params:		nil,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"GetBankCustomer resp error",
			args: args{
				mocks: []interface{}{
					mockBankCustomerClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(nil, errors.New("test err")),
				},
				ctx:		context.Background(),
				workflowEntity:	chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_USER_DETAILS,
				params:		userDetailsParams,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"GetVKYCSummary resp error",
			args: args{
				mocks: []interface{}{
					mockBankCustomerClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankCustPb.GetBankCustomerResponse{
						Status: rpcPb.StatusOk(),
					}, nil),
					mockVkycClient.EXPECT().GetVKYCSummary(gomock.Any(), gomock.Any()).Return(nil, errors.New("test err")),
				},
				ctx:		context.Background(),
				workflowEntity:	chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_USER_DETAILS,
				params:		userDetailsParams,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"successful - vkyc record not found, min kyc",
			args: args{
				mocks: []interface{}{
					mockBankCustomerClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankCustPb.GetBankCustomerResponse{
						Status:	rpcPb.StatusOk(),
						BankCustomer: &bankCustPb.BankCustomer{
							DedupeInfo:			&bankCustPb.DedupeInfo{KycLevel: kycPb.KYCLevel_MIN_KYC},
							VendorCreationSucceededAt:	accountCreationTime1,
						},
					}, nil),
					mockVkycClient.EXPECT().GetVKYCSummary(gomock.Any(), gomock.Any()).Return(&vkycPb.GetVKYCSummaryResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil),
				},
				ctx:		context.Background(),
				workflowEntity:	chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_USER_DETAILS,
				params:		userDetailsParams,
			},
			want:		userDetailsData3,
			wantErr:	false,
		},
		{
			name:	"successful - vkyc approved",
			args: args{
				mocks: []interface{}{
					mockBankCustomerClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankCustPb.GetBankCustomerResponse{
						Status:	rpcPb.StatusOk(),
						BankCustomer: &bankCustPb.BankCustomer{
							DedupeInfo:			&bankCustPb.DedupeInfo{KycLevel: kycPb.KYCLevel_FULL_KYC},
							VendorCreationSucceededAt:	accountCreationTime1,
						},
					}, nil),
					mockVkycClient.EXPECT().GetVKYCSummary(gomock.Any(), gomock.Any()).Return(&vkycPb.GetVKYCSummaryResponse{
						Status:	rpcPb.StatusOk(),
						VkycRecord: &vkycPb.VKYCRecord{
							VkycAttemptDataList:	[]*vkycPb.VKYCRecord_VKYCAttemptData{{VkycAttempt: &vkycPb.VKYCAttempt{}}},
							VkycSummary:		&vkycPb.VKYCSummary{Status: vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED}},
					}, nil),
				},
				ctx:		context.Background(),
				workflowEntity:	chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_USER_DETAILS,
				params:		userDetailsParams,
			},
			want:		userDetailsData,
			wantErr:	false,
		},
		{
			name:	"successful - vkyc never attempted full kyc",
			args: args{
				mocks: []interface{}{
					mockBankCustomerClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankCustPb.GetBankCustomerResponse{
						Status:	rpcPb.StatusOk(),
						BankCustomer: &bankCustPb.BankCustomer{
							DedupeInfo:			&bankCustPb.DedupeInfo{KycLevel: kycPb.KYCLevel_FULL_KYC},
							VendorCreationSucceededAt:	accountCreationTime2,
						},
					}, nil),
					mockVkycClient.EXPECT().GetVKYCSummary(gomock.Any(), gomock.Any()).Return(&vkycPb.GetVKYCSummaryResponse{
						Status: rpcPb.StatusOk(),
					}, nil),
				},
				ctx:		context.Background(),
				workflowEntity:	chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_USER_DETAILS,
				params:		userDetailsParams,
			},
			want:		userDetailsData2,
			wantErr:	false,
		},
		{
			name:	"successful - vkyc in review",
			args: args{
				mocks: []interface{}{
					mockBankCustomerClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankCustPb.GetBankCustomerResponse{
						Status:	rpcPb.StatusOk(),
						BankCustomer: &bankCustPb.BankCustomer{
							DedupeInfo:			&bankCustPb.DedupeInfo{KycLevel: kycPb.KYCLevel_FULL_KYC},
							VendorCreationSucceededAt:	accountCreationTime1,
						},
					}, nil),
					mockVkycClient.EXPECT().GetVKYCSummary(gomock.Any(), gomock.Any()).Return(&vkycPb.GetVKYCSummaryResponse{
						Status:	rpcPb.StatusOk(),
						VkycRecord: &vkycPb.VKYCRecord{
							VkycAttemptDataList:	[]*vkycPb.VKYCRecord_VKYCAttemptData{{VkycAttempt: &vkycPb.VKYCAttempt{}}},
							VkycSummary: &vkycPb.VKYCSummary{
								Status:		vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW,
								UpdatedAt:	vkycRecordUpdatedAtTime,
							}},
					}, nil),
				},
				ctx:		context.Background(),
				workflowEntity:	chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_USER_DETAILS,
				params:		userDetailsParams,
			},
			want:		userDetailsData4,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewUserDetailsProcessor(mockCustomerIdentifier, mockVkycClient, mockBankCustomerClient)
			got, err := s.FetchData(tt.args.ctx, tt.args.workflowEntity, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FetchData() got = %v, want %v", got, tt.want)
			}
		})
	}
}
