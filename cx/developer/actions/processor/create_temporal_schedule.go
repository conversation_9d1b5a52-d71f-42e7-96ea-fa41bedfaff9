//nolint:all
package processor

import (
	"context"
	"errors"
	"fmt"
	"strings"

	temporalSchedulePb "go.temporal.io/api/schedule/v1"
	temporalWorkflowPb "go.temporal.io/api/workflowservice/v1"

	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	epifitemporalSchedule "github.com/epifi/be-common/pkg/epifitemporal/schedule"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
)

type CreateTemporalSchedule struct {
	wfServiceClient temporalWorkflowPb.WorkflowServiceClient
}

func NewCreateTemporalSchedule(wfServiceClient temporalWorkflowPb.WorkflowServiceClient) *CreateTemporalSchedule {
	return &CreateTemporalSchedule{wfServiceClient: wfServiceClient}
}

func (c *CreateTemporalSchedule) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            wfClient,
			Label:           "Client",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         celestialPkg.GetWorkflowClientEnums(),
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            wfName,
			Label:           "Workflow Name which needs to be executed",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            scheduleClientReqId,
			Label:           "Schedule Client Req Id (unique identifier for creating schedule)",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            scheduleCronExpression,
			Label:           "Schedule Cron Expression (should be of the format Seconds Minutes Hours DayOfMonth Month DayOfWeek Year)",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (c *CreateTemporalSchedule) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var (
		workflowName, scheduleClientRequestId string
		workflowClient                        workflow.Client
		cronExpression                        string
		scheduleIdentifier                    string
		err                                   error
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case wfClient:
			workflowClient = workflow.Client(workflow.Client_value[filter.GetDropdownValue()])
		case wfName:
			workflowName = filter.GetStringValue()
		case scheduleClientReqId:
			scheduleClientRequestId = filter.GetStringValue()
		case scheduleCronExpression:
			cronExpression = filter.GetStringValue()
		}
	}
	calenderSpec, err := getCalenderSpec(cronExpression)
	if err != nil {
		return "error in parsing to calender spec", fmt.Errorf("error in parsing to calender spec %w", err)
	}
	temporalScheduleSpec := &temporalSchedulePb.ScheduleSpec{
		Calendar: []*temporalSchedulePb.CalendarSpec{calenderSpec},
	}
	scheduleIdentifier, err = epifitemporalSchedule.CreateSchedule(ctx, c.wfServiceClient, temporalScheduleSpec, &workflow.ClientReqId{
		Id:     scheduleClientRequestId,
		Client: workflowClient,
	}, epifitemporal.Workflow(workflowName))
	if err != nil {
		return "", fmt.Errorf("error in creating schedule %w", err)
	}
	return scheduleIdentifier, nil
}

// getCalenderSpec converts cron string to calender Spec
// NOTE : string should be of the format : * * * * * *
func getCalenderSpec(cronString string) (*temporalSchedulePb.CalendarSpec, error) {
	cronString = strings.TrimSpace(cronString)
	splits := strings.Split(cronString, " ")
	if len(splits) != 7 {
		return nil, fmt.Errorf("error in converting string to calender spec %v", len(splits))
	}
	return &temporalSchedulePb.CalendarSpec{
		Second:     splits[0],
		Minute:     splits[1],
		Hour:       splits[2],
		DayOfMonth: splits[3],
		Month:      splits[4],
		DayOfWeek:  splits[5],
		Year:       splits[6],
	}, nil
}
