//nolint:dupl
package processor

import (
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	cxPb "github.com/epifi/gamma/api/cx"
	adminActionsPb "github.com/epifi/gamma/api/cx/admin_actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type RevokeUserAccess struct {
	actorClient actor.ActorClient
	authClient  authPb.AuthClient
	usersClient userPb.UsersClient
}

func NewRevokeUserAccess(actorClient actor.ActorClient, authClient authPb.AuthClient, usersClient userPb.UsersClient) *RevokeUserAccess {
	return &RevokeUserAccess{actorClient: actorClient, authClient: authClient, usersClient: usersClient}
}

func (u *RevokeUserAccess) FetchParamList(ctx context.Context, action adminActionsPb.AdminAction) ([]*dsPb.ParameterMeta, error) {

	paramList := []*dsPb.ParameterMeta{}
	return paramList, nil
}

func (u *RevokeUserAccess) ExecuteAction(ctx context.Context, action adminActionsPb.AdminAction, filters []*dsPb.Filter,
	header *cxPb.Header) (string, error) {
	// delete auth tokens only when userPb access is to be revoked
	// we need to mark userPb as soft block in this case
	if err := u.deleteToken(ctx, header.GetUser()); err != nil {
		logger.Error(ctx, "error in deleting auth tokens", zap.Error(err))
		return "", status.Error(codes.Internal, fmt.Sprintf("error in deleting auth tokens: %v", err))
	}
	// set AccessRevokeState to soft block and update user
	header.GetUser().AccessRevokeState = userPb.AccessRevokeState_ACCESS_REVOKE_STATE_SOFT_BLOCK
	userUpdResp, err := u.usersClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User:       header.GetUser(),
		UpdateMask: []userPb.UserFieldMask{userPb.UserFieldMask_ACCESS_REVOKE_STATE},
	})
	if err != nil {
		logger.Error(ctx, "unable to update userPb", zap.Error(err))
		jsonResp := []byte(err.Error())
		return string(jsonResp), nil
	}
	jsonResp, err := json.Marshal(userUpdResp)
	if err != nil {
		logger.Error(ctx, "error marshalling UpdateUser response", zap.Error(err))
		jsonResp = []byte(err.Error())
	}
	return string(jsonResp), nil
}

func (u *RevokeUserAccess) deleteToken(ctx context.Context, user *userPb.User) error {
	delAuthToken, err := u.authClient.UpdateToken(ctx, &authPb.UpdateTokenRequest{
		Status: authPb.UpdateTokenRequest_DELETE,
		Identifier: &authPb.UpdateTokenRequest_PhoneNumber{
			PhoneNumber: user.GetProfile().GetPhoneNumber(),
		},
		TokenTypes: []authPb.TokenType{
			authPb.TokenType_ACCESS_TOKEN,
			authPb.TokenType_REFRESH_TOKEN,
		},
		TokenUpdationReason: authPb.TokenDeletionReason_TOKEN_DELETION_REASON_ACCESS_REVOKED,
	})
	if te := epifigrpc.RPCError(delAuthToken, err); te != nil {
		return te
	}
	return nil
}
