DELETE FROM workflow_histories;
DELETE FROM workflow_requests;
INSERT
INTO workflow_requests
(id, actor_id, stage, status, version, type, payload, client_req_id, ownership)
VALUES ('6535e977-9107-47f3-9eb7-6aa5e072da30', 'newActorId', 'CREATION', 'INITIATED', 'V0', 'SELL_US_STOCKS', NULL,
		'client-req-id-1', 'US_STOCKS_ALPACA'),
	   ('d09e5b74-9abf-11ec-b909-0242ac120002', 'newActorId', 'PAYMENT', 'SUCCESSFUL', 'V0', 'SELL_US_STOCKS', NULL,
		'client-req-id-2', 'US_STOCKS_ALPACA'),
	   ('9ef656fc-3e12-4214-838b-f62485655e41', 'actorId', 'COLLECT', 'BLOCKED', 'V0', 'TYPE_UNSPECIFIED', NULL,
		'CiQ4NTY4YzEyZi0xYWMzLTQ3ZjEtOTIwOC0zZmRlMjAwMGNkYTQ=', 'US_STOCKS_ALPACA'),
	   ('9ef656fc-3e12-4214-838b-f62485655e42', 'actorId', 'COLLECT', 'BLOCKED', 'V0', 'TYPE_UNSPECIFIED', NULL,
		'CiQ5ZWY2NTZmYy0zZTEyLTQyMTQtODM4Yi1mNjI0ODU2NTVlNDA=', 'US_STOCKS_ALPACA'),
	   ('9ef656fc-3e12-4214-838b-f62485655e43', 'actorId', 'COLLECT', 'BLOCKED', 'V0', 'TYPE_UNSPECIFIED', NULL,
		'CiQ5ZWY2NTZmYy0zZTEyLTQyMTQtODM4Yi1mNjI0ODU2NTVlNDQ=', 'US_STOCKS_ALPACA'),
	   ('9ef656fc-3e12-4214-838b-f62485655e44', 'actorId', 'COLLECT', 'BLOCKED', 'V0', 'B2C_FUND_TRANSFER', NULL,
		'ChcyZmJhMGEyNi0wYzE4LTExZWQtODYxZBAD', 'US_STOCKS_ALPACA'),
	   ('9ef656fc-3e12-4214-838b-f62485655e45', 'actorId', 'COLLECT', 'BLOCKED', 'V0', 'B2C_FUND_TRANSFER', NULL,
		'ChdhNTAwNGYxOC01ZDUyLTQ5OTEtODJhORAD', 'US_STOCKS_ALPACA'),
	   ('9ef656fd-3e12-4214-838b-f62485655e44', 'AC220923JcsbsNMWQY+ZHOL0i/ApGg==', 'SEND_BUY_ORDER', 'INITIATED', 'V0',
		'BUY_US_STOCKS', NULL, 'ChdhNTAwNGYxOC01ZDUyLTQ5OTEtODJhORAA', 'US_STOCKS_ALPACA'),
	   ('9ef656fd-3e12-4214-838b-f62485655e47', 'AC220923JcsbsNMWQY+ZHOL0i/ApGg==', 'SEND_BUY_ORDER', 'INITIATED', 'V0',
		'BUY_US_STOCKS', NULL, 'ChdhNTAwNGYxOC01ZDUyLTQ5OTEtODJhORAB', 'US_STOCKS_ALPACA'),
	   ('9ef656fc-3e12-4214-838b-f62485655e46', 'actorId', 'COLLECT', 'BLOCKED', 'V0','B2C_FUND_TRANSFER',NULL,
		'ChdhNTAwNGYxOC01ZDUyLTQ5OTEtODJhORAE', 'US_STOCKS_ALPACA');

INSERT
INTO workflow_histories
(wf_req_id, stage, status, attempts, created_at)
VALUES ('9ef656fd-3e12-4214-838b-f62485655e44', 'POOL_ACCOUNT_TRANSFER', 'SUCCESSFUL', 1, now()-'20 second'::interval),
	   ('9ef656fd-3e12-4214-838b-f62485655e44', 'SEND_BUY_ORDER', 'SUCCESSFUL', 1, now()-'10 second'::interval),
	   ('9ef656fd-3e12-4214-838b-f62485655e44', 'TRACK_ORDER', 'INITIATED', 1, now()),

	   ('9ef656fd-3e12-4214-838b-f62485655e47', 'POOL_ACCOUNT_TRANSFER', 'SUCCESSFUL', 1, now()-'50 second'::interval),
	   ('9ef656fd-3e12-4214-838b-f62485655e47', 'SEND_BUY_ORDER', 'SUCCESSFUL', 1, now()-'40 second'::interval),
	   ('9ef656fd-3e12-4214-838b-f62485655e47', 'TRACK_ORDER', 'INITIATED', 1, now()-'30 second'::interval);

INSERT
INTO workflow_histories
(wf_req_id, stage, status, attempts)
VALUES ('6535e977-9107-47f3-9eb7-6aa5e072da30', 'CREATION', 'INITIATED', 1),
	   ('d09e5b74-9abf-11ec-b909-0242ac120002', 'PAYMENT', 'SUCCESSFUL', 1),
	   ('d09e5b74-9abf-11ec-b909-0242ac120002', 'FULFILLMENT', 'SUCCESSFUL', 1),
	   ('9ef656fc-3e12-4214-838b-f62485655e46', 'CREATION', 'CREATED', 1);

