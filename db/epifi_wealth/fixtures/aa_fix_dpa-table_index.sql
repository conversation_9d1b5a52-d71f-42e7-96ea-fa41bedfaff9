-- create unique index on table aa_data_process_attempts on columns attempt_id, fip_id, link_ref_number and drop existing index attempt_id_fip_id_data_process_status
CREATE UNIQUE INDEX IF NOT EXISTS attempt_id_fip_id_link_ref_number_unique_idx on aa_data_process_attempts(attempt_id ASC, fip_id ASC, link_ref_number ASC);
DROP INDEX IF EXISTS aa_data_process_attempts@attempt_id_fip_id_data_process_status CASCADE;
