-- moving states of the workflow manually to success [Current state: MANUAL_INTERVENTION] --
-- context: IFT workflow completed execution and so sending order fulfillment signal failed, which resulted moving US Stocks workflow to MANUAL INTERVENTION --
update workflow_requests set stage = 'RELEASE_SELL_LOCK', status = 'SUCCESSFUL', updated_at = now() where id in ('WFR2303168HAIKYs1S3mAGkXTlnQMmA==', 'WFR230315Jg8HlX63T0a0gavpAFflLA==');
update workflow_histories set status = 'SUCCESSFUL', updated_at = now() where stage = 'TRACK_ORDER' AND wf_req_id in ('WFR2303168HAIKYs1S3mAGkXTlnQMmA==', 'WFR230315Jg8HlX63T0a0gavpAFflLA==');
update orders set state = 'ORDER_SUCCESS', updated_at = now() where wf_req_id in ('WFR2303168HAIKYs1S3mAGkXTlnQMmA==', 'WFR230315Jg8HlX63T0a0gavpAFflLA==');
insert into workflow_histories (id, wf_req_id, stage, status, completed_at) values
    (gen_random_uuid(), 'WFR2303168HAIKYs1S3mAGkXTlnQMmA==' ,'FOREIGN_FUND_TRANSFER', 'SUCCESSFUL', now()),
    (gen_random_uuid(), 'WFR2303168HAIKYs1S3mAGkXTlnQMmA==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now()),
    (gen_random_uuid(), 'WFR230315Jg8HlX63T0a0gavpAFflLA==' ,'FOREIGN_FUND_TRANSFER', 'SUCCESSFUL', now()),
    (gen_random_uuid(), 'WFR230315Jg8HlX63T0a0gavpAFflLA==' ,'RELEASE_SELL_LOCK', 'SUCCESSFUL', now());
