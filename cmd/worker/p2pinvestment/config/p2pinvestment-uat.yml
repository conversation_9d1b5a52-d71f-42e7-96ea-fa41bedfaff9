Application:
  Environment: "uat"
  Namespace: "uat-p2pinvestment"
  TaskQueue: "uat-p2pinvestment-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: ""
      DB: 0

DbConfigMap:
  P2P_INVESTMENT_LIQUILOANS:
    DBType: "CRDB"
    StatementTimeout: 5s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  P2P_INVESTMENT_LIQUILOANS:
    DBType: "CRDB"
    StatementTimeout: 5s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

WorkflowUpdatePublisher:
  TopicName: "uat-celestial-workflow-update-topic"

SchemeNameToMaturityNudgeMap:

InvestmentInstrumentEventPublisher:
  QueueName: "uat-investment-event-queue"

Secrets:
  Ids:
    RudderWriteKey: "uat/rudder/internal-writekey"
    TemporalCodecAesKey: "uat/temporal/codec-encryption-key"

RedisClusters:
  InvestmentRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 0
  MfCatalogRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 0
  P2pInvestmentRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 10
  USStocksRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
