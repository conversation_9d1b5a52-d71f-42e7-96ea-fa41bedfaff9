syntax = "proto3";

package api.typesv2.deeplink_screen_option.onboarding;

import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/deeplink_screen_option/header.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.onboarding";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// screen options v2 for SAVINGS_ACCOUNT_SETUP_PROGRESS
message GetNextOnbActionScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  api.typesv2.common.VisualElement image = 2;
  api.typesv2.common.Text title = 3;
  api.typesv2.common.Text subtitle = 4;
  // onboarding feature for which the next action has to be fetched
  // Client should pass this in the GetNextOnboardingAction request
  string feature = 5;
  string feature_onboarding_entry_point = 6;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 7;
  // Repeated field to display trust markers, which consist of icons and text,
  // helping to build user confidence in the service/product.
  repeated ui.IconTextComponent bottom_info_cards = 8;
}
