// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/namecheck/employernamecategoriser/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	employernamecategoriser "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockEmployerNameCategoriserClient is a mock of EmployerNameCategoriserClient interface.
type MockEmployerNameCategoriserClient struct {
	ctrl     *gomock.Controller
	recorder *MockEmployerNameCategoriserClientMockRecorder
}

// MockEmployerNameCategoriserClientMockRecorder is the mock recorder for MockEmployerNameCategoriserClient.
type MockEmployerNameCategoriserClientMockRecorder struct {
	mock *MockEmployerNameCategoriserClient
}

// NewMockEmployerNameCategoriserClient creates a new mock instance.
func NewMockEmployerNameCategoriserClient(ctrl *gomock.Controller) *MockEmployerNameCategoriserClient {
	mock := &MockEmployerNameCategoriserClient{ctrl: ctrl}
	mock.recorder = &MockEmployerNameCategoriserClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEmployerNameCategoriserClient) EXPECT() *MockEmployerNameCategoriserClientMockRecorder {
	return m.recorder
}

// EmployerNameCategoriser mocks base method.
func (m *MockEmployerNameCategoriserClient) EmployerNameCategoriser(ctx context.Context, in *employernamecategoriser.EmployerNameCategoriserRequest, opts ...grpc.CallOption) (*employernamecategoriser.EmployerNameCategoriserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EmployerNameCategoriser", varargs...)
	ret0, _ := ret[0].(*employernamecategoriser.EmployerNameCategoriserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EmployerNameCategoriser indicates an expected call of EmployerNameCategoriser.
func (mr *MockEmployerNameCategoriserClientMockRecorder) EmployerNameCategoriser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EmployerNameCategoriser", reflect.TypeOf((*MockEmployerNameCategoriserClient)(nil).EmployerNameCategoriser), varargs...)
}

// MockEmployerNameCategoriserServer is a mock of EmployerNameCategoriserServer interface.
type MockEmployerNameCategoriserServer struct {
	ctrl     *gomock.Controller
	recorder *MockEmployerNameCategoriserServerMockRecorder
}

// MockEmployerNameCategoriserServerMockRecorder is the mock recorder for MockEmployerNameCategoriserServer.
type MockEmployerNameCategoriserServerMockRecorder struct {
	mock *MockEmployerNameCategoriserServer
}

// NewMockEmployerNameCategoriserServer creates a new mock instance.
func NewMockEmployerNameCategoriserServer(ctrl *gomock.Controller) *MockEmployerNameCategoriserServer {
	mock := &MockEmployerNameCategoriserServer{ctrl: ctrl}
	mock.recorder = &MockEmployerNameCategoriserServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEmployerNameCategoriserServer) EXPECT() *MockEmployerNameCategoriserServerMockRecorder {
	return m.recorder
}

// EmployerNameCategoriser mocks base method.
func (m *MockEmployerNameCategoriserServer) EmployerNameCategoriser(arg0 context.Context, arg1 *employernamecategoriser.EmployerNameCategoriserRequest) (*employernamecategoriser.EmployerNameCategoriserResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EmployerNameCategoriser", arg0, arg1)
	ret0, _ := ret[0].(*employernamecategoriser.EmployerNameCategoriserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EmployerNameCategoriser indicates an expected call of EmployerNameCategoriser.
func (mr *MockEmployerNameCategoriserServerMockRecorder) EmployerNameCategoriser(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EmployerNameCategoriser", reflect.TypeOf((*MockEmployerNameCategoriserServer)(nil).EmployerNameCategoriser), arg0, arg1)
}

// MockUnsafeEmployerNameCategoriserServer is a mock of UnsafeEmployerNameCategoriserServer interface.
type MockUnsafeEmployerNameCategoriserServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeEmployerNameCategoriserServerMockRecorder
}

// MockUnsafeEmployerNameCategoriserServerMockRecorder is the mock recorder for MockUnsafeEmployerNameCategoriserServer.
type MockUnsafeEmployerNameCategoriserServerMockRecorder struct {
	mock *MockUnsafeEmployerNameCategoriserServer
}

// NewMockUnsafeEmployerNameCategoriserServer creates a new mock instance.
func NewMockUnsafeEmployerNameCategoriserServer(ctrl *gomock.Controller) *MockUnsafeEmployerNameCategoriserServer {
	mock := &MockUnsafeEmployerNameCategoriserServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeEmployerNameCategoriserServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeEmployerNameCategoriserServer) EXPECT() *MockUnsafeEmployerNameCategoriserServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedEmployerNameCategoriserServer mocks base method.
func (m *MockUnsafeEmployerNameCategoriserServer) mustEmbedUnimplementedEmployerNameCategoriserServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedEmployerNameCategoriserServer")
}

// mustEmbedUnimplementedEmployerNameCategoriserServer indicates an expected call of mustEmbedUnimplementedEmployerNameCategoriserServer.
func (mr *MockUnsafeEmployerNameCategoriserServerMockRecorder) mustEmbedUnimplementedEmployerNameCategoriserServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedEmployerNameCategoriserServer", reflect.TypeOf((*MockUnsafeEmployerNameCategoriserServer)(nil).mustEmbedUnimplementedEmployerNameCategoriserServer))
}
