// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/offers/loylty/loylty_gift_card.proto

package loylty

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoyltyGiftCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string `protobuf:"bytes,1,opt,name=id,json=Id,proto3" json:"id,omitempty"`
	CardNo         string `protobuf:"bytes,2,opt,name=cardNo,json=CardNo,proto3" json:"cardNo,omitempty"`
	Expiry         string `protobuf:"bytes,3,opt,name=expiry,json=Expiry,proto3" json:"expiry,omitempty"`
	ActivationCode string `protobuf:"bytes,4,opt,name=activation_code,json=ActivationCode,proto3" json:"activation_code,omitempty"`
	Pin            string `protobuf:"bytes,5,opt,name=pin,json=Pin,proto3" json:"pin,omitempty"`
	Url            string `protobuf:"bytes,6,opt,name=url,json=Url,proto3" json:"url,omitempty"`
	Balance        int32  `protobuf:"varint,7,opt,name=balance,json=Balance,proto3" json:"balance,omitempty"`
}

func (x *LoyltyGiftCard) Reset() {
	*x = LoyltyGiftCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoyltyGiftCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoyltyGiftCard) ProtoMessage() {}

func (x *LoyltyGiftCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoyltyGiftCard.ProtoReflect.Descriptor instead.
func (*LoyltyGiftCard) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDescGZIP(), []int{0}
}

func (x *LoyltyGiftCard) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoyltyGiftCard) GetCardNo() string {
	if x != nil {
		return x.CardNo
	}
	return ""
}

func (x *LoyltyGiftCard) GetExpiry() string {
	if x != nil {
		return x.Expiry
	}
	return ""
}

func (x *LoyltyGiftCard) GetActivationCode() string {
	if x != nil {
		return x.ActivationCode
	}
	return ""
}

func (x *LoyltyGiftCard) GetPin() string {
	if x != nil {
		return x.Pin
	}
	return ""
}

func (x *LoyltyGiftCard) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *LoyltyGiftCard) GetBalance() int32 {
	if x != nil {
		return x.Balance
	}
	return 0
}

var File_api_vendorgateway_offers_loylty_loylty_gift_card_proto protoreflect.FileDescriptor

var file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDesc = []byte{
	0x0a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2f, 0x6c, 0x6f, 0x79, 0x6c, 0x74,
	0x79, 0x2f, 0x6c, 0x6f, 0x79, 0x6c, 0x74, 0x79, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2e, 0x6c,
	0x6f, 0x79, 0x6c, 0x74, 0x79, 0x22, 0xb7, 0x01, 0x0a, 0x0e, 0x4c, 0x6f, 0x79, 0x6c, 0x74, 0x79,
	0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x72, 0x64,
	0x4e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x6f,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x50, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42,
	0x70, 0x0a, 0x36, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x2e, 0x6c, 0x6f, 0x79, 0x6c, 0x74, 0x79, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x2f, 0x6c, 0x6f, 0x79, 0x6c, 0x74,
	0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDescOnce sync.Once
	file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDescData = file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDesc
)

func file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDescData)
	})
	return file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDescData
}

var file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_goTypes = []interface{}{
	(*LoyltyGiftCard)(nil), // 0: vendorgateway.offers.loylty.LoyltyGiftCard
}
var file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_init() }
func file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_init() {
	if File_api_vendorgateway_offers_loylty_loylty_gift_card_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoyltyGiftCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_depIdxs,
		MessageInfos:      file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_offers_loylty_loylty_gift_card_proto = out.File
	file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_rawDesc = nil
	file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_goTypes = nil
	file_api_vendorgateway_offers_loylty_loylty_gift_card_proto_depIdxs = nil
}
