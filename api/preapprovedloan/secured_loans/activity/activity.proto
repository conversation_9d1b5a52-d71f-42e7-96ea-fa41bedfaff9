syntax = "proto3";

package preapprovedloan.secured_loans.activity;

import "api/celestial/activity/header.proto";
import "api/preapprovedloan/loan_step_execution.proto";
import "api/preapprovedloan/enums.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan/secured_loans/activity";
option java_package = "com.github.epifi.gamma.api.preapprovedloan.secured_loans.activity";


message ActivityRequest {
  celestial.activity.RequestHeader request_header = 1;
  preapprovedloan.LoanStepExecution loan_step = 2;
  preapprovedloan.Vendor vendor = 3;
  preapprovedloan.LoanProgram loan_program = 4;
}

message ActivityResponse {
  celestial.activity.ResponseHeader response_header = 1;
}
