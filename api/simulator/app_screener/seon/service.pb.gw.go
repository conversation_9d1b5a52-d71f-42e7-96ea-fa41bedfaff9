// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: api/simulator/app_screener/seon/service.proto

/*
Package seon is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package seon

import (
	"context"
	"io"
	"net/http"

	seon_1 "github.com/epifi/gamma/api/vendors/appscreener/seon"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_Seon_GetUserSocialMediaInformationByEmail_0(ctx context.Context, marshaler runtime.Marshaler, client SeonClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq seon_1.GetUserSocialMediaInformationByEmailRequest
	var metadata runtime.ServerMetadata

	var (
		val string
		ok  bool
		err error
		_   = err
	)

	val, ok = pathParams["email_id"]
	if !ok {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "missing parameter %s", "email_id")
	}

	protoReq.EmailId, err = runtime.String(val)
	if err != nil {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "type mismatch, parameter: %s, error: %v", "email_id", err)
	}

	msg, err := client.GetUserSocialMediaInformationByEmail(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_Seon_GetUserSocialMediaInformationByEmail_0(ctx context.Context, marshaler runtime.Marshaler, server SeonServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq seon_1.GetUserSocialMediaInformationByEmailRequest
	var metadata runtime.ServerMetadata

	var (
		val string
		ok  bool
		err error
		_   = err
	)

	val, ok = pathParams["email_id"]
	if !ok {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "missing parameter %s", "email_id")
	}

	protoReq.EmailId, err = runtime.String(val)
	if err != nil {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "type mismatch, parameter: %s, error: %v", "email_id", err)
	}

	msg, err := server.GetUserSocialMediaInformationByEmail(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterSeonHandlerServer registers the http handlers for service Seon to "mux".
// UnaryRPC     :call SeonServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterSeonHandlerFromEndpoint instead.
func RegisterSeonHandlerServer(ctx context.Context, mux *runtime.ServeMux, server SeonServer) error {

	mux.Handle("GET", pattern_Seon_GetUserSocialMediaInformationByEmail_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/simulator.app_screener.seon.Seon/GetUserSocialMediaInformationByEmail", runtime.WithHTTPPathPattern("/GetUserInformationByEmailId/{email_id}"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_Seon_GetUserSocialMediaInformationByEmail_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_Seon_GetUserSocialMediaInformationByEmail_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterSeonHandlerFromEndpoint is same as RegisterSeonHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterSeonHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.DialContext(ctx, endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Infof("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Infof("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterSeonHandler(ctx, mux, conn)
}

// RegisterSeonHandler registers the http handlers for service Seon to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterSeonHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterSeonHandlerClient(ctx, mux, NewSeonClient(conn))
}

// RegisterSeonHandlerClient registers the http handlers for service Seon
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "SeonClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "SeonClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "SeonClient" to call the correct interceptors.
func RegisterSeonHandlerClient(ctx context.Context, mux *runtime.ServeMux, client SeonClient) error {

	mux.Handle("GET", pattern_Seon_GetUserSocialMediaInformationByEmail_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/simulator.app_screener.seon.Seon/GetUserSocialMediaInformationByEmail", runtime.WithHTTPPathPattern("/GetUserInformationByEmailId/{email_id}"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_Seon_GetUserSocialMediaInformationByEmail_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_Seon_GetUserSocialMediaInformationByEmail_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_Seon_GetUserSocialMediaInformationByEmail_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 1, 0, 4, 1, 5, 1}, []string{"GetUserInformationByEmailId", "email_id"}, ""))
)

var (
	forward_Seon_GetUserSocialMediaInformationByEmail_0 = runtime.ForwardResponseMessage
)
