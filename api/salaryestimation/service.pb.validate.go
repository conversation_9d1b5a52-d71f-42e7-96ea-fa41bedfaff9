// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/salaryestimation/service.proto

package salaryestimation

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	salaryestimation "github.com/epifi/gamma/api/typesv2/salaryestimation"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = salaryestimation.Source(0)
)

// Validate checks the field values on GetSalaryRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetSalaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSalaryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSalaryRequestMultiError, or nil if none found.
func (m *GetSalaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSalaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetSalaryRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSalaryRequestMultiError(errors)
	}

	return nil
}

// GetSalaryRequestMultiError is an error wrapping multiple validation errors
// returned by GetSalaryRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSalaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSalaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSalaryRequestMultiError) AllErrors() []error { return m }

// GetSalaryRequestValidationError is the validation error returned by
// GetSalaryRequest.Validate if the designated constraints aren't met.
type GetSalaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSalaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSalaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSalaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSalaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSalaryRequestValidationError) ErrorName() string { return "GetSalaryRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetSalaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSalaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSalaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSalaryRequestValidationError{}

// Validate checks the field values on GetSalaryResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetSalaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSalaryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSalaryResponseMultiError, or nil if none found.
func (m *GetSalaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSalaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSalaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSalaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSalaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSalary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSalaryResponseValidationError{
					field:  "Salary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSalaryResponseValidationError{
					field:  "Salary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSalary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSalaryResponseValidationError{
				field:  "Salary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for L1AnalysisSignedUrl

	if len(errors) > 0 {
		return GetSalaryResponseMultiError(errors)
	}

	return nil
}

// GetSalaryResponseMultiError is an error wrapping multiple validation errors
// returned by GetSalaryResponse.ValidateAll() if the designated constraints
// aren't met.
type GetSalaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSalaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSalaryResponseMultiError) AllErrors() []error { return m }

// GetSalaryResponseValidationError is the validation error returned by
// GetSalaryResponse.Validate if the designated constraints aren't met.
type GetSalaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSalaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSalaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSalaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSalaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSalaryResponseValidationError) ErrorName() string {
	return "GetSalaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSalaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSalaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSalaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSalaryResponseValidationError{}

// Validate checks the field values on Salary with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Salary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Salary with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SalaryMultiError, or nil if none found.
func (m *Salary) ValidateAll() error {
	return m.validate(true)
}

func (m *Salary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Source

	if all {
		switch v := interface{}(m.GetSalaryAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryValidationError{
					field:  "SalaryAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryValidationError{
					field:  "SalaryAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSalaryAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryValidationError{
				field:  "SalaryAccount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetComputedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SalaryValidationError{
					field:  "ComputedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SalaryValidationError{
					field:  "ComputedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComputedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SalaryValidationError{
				field:  "ComputedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SalaryMultiError(errors)
	}

	return nil
}

// SalaryMultiError is an error wrapping multiple validation errors returned by
// Salary.ValidateAll() if the designated constraints aren't met.
type SalaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SalaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SalaryMultiError) AllErrors() []error { return m }

// SalaryValidationError is the validation error returned by Salary.Validate if
// the designated constraints aren't met.
type SalaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SalaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SalaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SalaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SalaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SalaryValidationError) ErrorName() string { return "SalaryValidationError" }

// Error satisfies the builtin error interface
func (e SalaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSalary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SalaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SalaryValidationError{}

// Validate checks the field values on SalaryAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SalaryAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SalaryAccount with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SalaryAccountMultiError, or
// nil if none found.
func (m *SalaryAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *SalaryAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if len(errors) > 0 {
		return SalaryAccountMultiError(errors)
	}

	return nil
}

// SalaryAccountMultiError is an error wrapping multiple validation errors
// returned by SalaryAccount.ValidateAll() if the designated constraints
// aren't met.
type SalaryAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SalaryAccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SalaryAccountMultiError) AllErrors() []error { return m }

// SalaryAccountValidationError is the validation error returned by
// SalaryAccount.Validate if the designated constraints aren't met.
type SalaryAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SalaryAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SalaryAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SalaryAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SalaryAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SalaryAccountValidationError) ErrorName() string { return "SalaryAccountValidationError" }

// Error satisfies the builtin error interface
func (e SalaryAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSalaryAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SalaryAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SalaryAccountValidationError{}

// Validate checks the field values on ComputeSalaryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ComputeSalaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComputeSalaryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ComputeSalaryRequestMultiError, or nil if none found.
func (m *ComputeSalaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ComputeSalaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ComputeSalaryRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Client

	if utf8.RuneCountInString(m.GetClientReqId()) < 1 {
		err := ComputeSalaryRequestValidationError{
			field:  "ClientReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Source

	if all {
		switch v := interface{}(m.GetSourceFlowParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComputeSalaryRequestValidationError{
					field:  "SourceFlowParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComputeSalaryRequestValidationError{
					field:  "SourceFlowParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceFlowParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComputeSalaryRequestValidationError{
				field:  "SourceFlowParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequireHoldingScreen

	if len(errors) > 0 {
		return ComputeSalaryRequestMultiError(errors)
	}

	return nil
}

// ComputeSalaryRequestMultiError is an error wrapping multiple validation
// errors returned by ComputeSalaryRequest.ValidateAll() if the designated
// constraints aren't met.
type ComputeSalaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComputeSalaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComputeSalaryRequestMultiError) AllErrors() []error { return m }

// ComputeSalaryRequestValidationError is the validation error returned by
// ComputeSalaryRequest.Validate if the designated constraints aren't met.
type ComputeSalaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComputeSalaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComputeSalaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComputeSalaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComputeSalaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComputeSalaryRequestValidationError) ErrorName() string {
	return "ComputeSalaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ComputeSalaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComputeSalaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComputeSalaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComputeSalaryRequestValidationError{}

// Validate checks the field values on ComputeSalaryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ComputeSalaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ComputeSalaryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ComputeSalaryResponseMultiError, or nil if none found.
func (m *ComputeSalaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ComputeSalaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComputeSalaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComputeSalaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComputeSalaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ComputeSalaryResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ComputeSalaryResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ComputeSalaryResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ComputeSalaryResponseMultiError(errors)
	}

	return nil
}

// ComputeSalaryResponseMultiError is an error wrapping multiple validation
// errors returned by ComputeSalaryResponse.ValidateAll() if the designated
// constraints aren't met.
type ComputeSalaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComputeSalaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComputeSalaryResponseMultiError) AllErrors() []error { return m }

// ComputeSalaryResponseValidationError is the validation error returned by
// ComputeSalaryResponse.Validate if the designated constraints aren't met.
type ComputeSalaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComputeSalaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComputeSalaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComputeSalaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComputeSalaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComputeSalaryResponseValidationError) ErrorName() string {
	return "ComputeSalaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ComputeSalaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComputeSalaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComputeSalaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComputeSalaryResponseValidationError{}
