//go:generate gen_sql -types=LRSCheckProvenance,LRSStatus
syntax = "proto3";

package api.pay.internationalfundtransfer;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/pay/internationalfundtransfer";
option java_package = "com.github.epifi.gamma.api.pay.internationalfundtransfer";

// LRSCheck is the table where we store all the entry and corresponding provenance for lrs checks for an actorId
message LRSCheck {
  // unique identifier for order
  string id = 1;

  // actor for whom we are storing the checks
  string actor_id = 2;

  // LRS limit of the actor as specified by the RBI, used to fail fast if the user has already breached the limit
  google.type.Money lrs_limit_consumed = 3;

  // entry point from where lrs checks is triggered, eg: pre-launch, account opening, buy flow
  LRSCheckProvenance provenance = 4;

  // used to get status of lrs report
  LRSStatus lrs_status = 5;

  google.protobuf.Timestamp created_at = 6;

  google.protobuf.Timestamp updated_at = 7;

  google.protobuf.Timestamp deleted_at = 8;
}

enum LRSCheckFieldMask {
  LRS_CHECK_FIELD_MASK_UNSPECIFIED = 0;
  LRS_CHECK_FIELD_MASK_ID = 1;
  LRS_CHECK_FIELD_MASK_ACTOR_ID = 2;
  LRS_CHECK_FIELD_MASK_LRS_LIMIT_CONSUMED = 3;
  LRS_CHECK_FIELD_MASK_PROVENANCE = 4;
  LRS_CHECK_FIELD_MASK_LRS_CHECK_STATUS = 5;
  LRS_CHECK_FIELD_MASK_CREATED_AT = 6;
  LRS_CHECK_FIELD_MASK_UPDATED_AT = 7;
  LRS_CHECK_FIELD_MASK_DELETED_AT = 8;
}

// LRSChecksStatus is used to get status of lrs report
enum LRSStatus {
  LRS_STATUS_UNSPECIFIED = 0;
  // when entry is created on lrs_checks table
  LRS_STATUS_INITIATED = 1;
  // when request is sent for get lrs consumed limit
  LRS_STATUS_IN_PROGRESS = 2;
  // when lrs consumed limit is received from vendor side
  LRS_STATUS_COMPLETED = 3;
}

// entry point from where lrs check is triggered
enum LRSCheckProvenance {
  LRS_CHECK_PROVENANCE_UNSPECIFIED = 0;
  LRS_CHECK_PROVENANCE_PRE_LAUNCH = 1;
  LRS_CHECK_PROVENANCE_POST_ACCOUNT_OPENING = 2;
  LRS_CHECK_PROVENANCE_BUY_FLOW = 3;
}
