// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/pay/beneficiarymanagement/beneficiary.proto

package beneficiarymanagement

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Beneficiary with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Beneficiary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Beneficiary with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BeneficiaryMultiError, or
// nil if none found.
func (m *Beneficiary) ValidateAll() error {
	return m.validate(true)
}

func (m *Beneficiary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorFrom

	// no validation rules for PiTo

	if all {
		switch v := interface{}(m.GetCooldownStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BeneficiaryValidationError{
					field:  "CooldownStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BeneficiaryValidationError{
					field:  "CooldownStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCooldownStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BeneficiaryValidationError{
				field:  "CooldownStartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCooldownEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BeneficiaryValidationError{
					field:  "CooldownEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BeneficiaryValidationError{
					field:  "CooldownEndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCooldownEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BeneficiaryValidationError{
				field:  "CooldownEndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BeneficiaryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BeneficiaryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BeneficiaryValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BeneficiaryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BeneficiaryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BeneficiaryValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BeneficiaryMultiError(errors)
	}

	return nil
}

// BeneficiaryMultiError is an error wrapping multiple validation errors
// returned by Beneficiary.ValidateAll() if the designated constraints aren't met.
type BeneficiaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BeneficiaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BeneficiaryMultiError) AllErrors() []error { return m }

// BeneficiaryValidationError is the validation error returned by
// Beneficiary.Validate if the designated constraints aren't met.
type BeneficiaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BeneficiaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BeneficiaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BeneficiaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BeneficiaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BeneficiaryValidationError) ErrorName() string { return "BeneficiaryValidationError" }

// Error satisfies the builtin error interface
func (e BeneficiaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBeneficiary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BeneficiaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BeneficiaryValidationError{}
