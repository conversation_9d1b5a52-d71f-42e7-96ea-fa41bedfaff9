// Code generated by MockGen. DO NOT EDIT.
// Source: api/stockguardian/sgexternalgateway/lead/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	lead "github.com/epifi/gringott/api/stockguardian/sgexternalgateway/lead"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockLeadClient is a mock of LeadClient interface.
type MockLeadClient struct {
	ctrl     *gomock.Controller
	recorder *MockLeadClientMockRecorder
}

// MockLeadClientMockRecorder is the mock recorder for MockLeadClient.
type MockLeadClientMockRecorder struct {
	mock *MockLeadClient
}

// NewMockLeadClient creates a new mock instance.
func NewMockLeadClient(ctrl *gomock.Controller) *MockLeadClient {
	mock := &MockLeadClient{ctrl: ctrl}
	mock.recorder = &MockLeadClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLeadClient) EXPECT() *MockLeadClientMockRecorder {
	return m.recorder
}

// CreateLead mocks base method.
func (m *MockLeadClient) CreateLead(ctx context.Context, in *lead.CreateLeadRequest, opts ...grpc.CallOption) (*lead.CreateLeadResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLead", varargs...)
	ret0, _ := ret[0].(*lead.CreateLeadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLead indicates an expected call of CreateLead.
func (mr *MockLeadClientMockRecorder) CreateLead(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLead", reflect.TypeOf((*MockLeadClient)(nil).CreateLead), varargs...)
}

// MockLeadServer is a mock of LeadServer interface.
type MockLeadServer struct {
	ctrl     *gomock.Controller
	recorder *MockLeadServerMockRecorder
}

// MockLeadServerMockRecorder is the mock recorder for MockLeadServer.
type MockLeadServerMockRecorder struct {
	mock *MockLeadServer
}

// NewMockLeadServer creates a new mock instance.
func NewMockLeadServer(ctrl *gomock.Controller) *MockLeadServer {
	mock := &MockLeadServer{ctrl: ctrl}
	mock.recorder = &MockLeadServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLeadServer) EXPECT() *MockLeadServerMockRecorder {
	return m.recorder
}

// CreateLead mocks base method.
func (m *MockLeadServer) CreateLead(arg0 context.Context, arg1 *lead.CreateLeadRequest) (*lead.CreateLeadResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLead", arg0, arg1)
	ret0, _ := ret[0].(*lead.CreateLeadResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLead indicates an expected call of CreateLead.
func (mr *MockLeadServerMockRecorder) CreateLead(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLead", reflect.TypeOf((*MockLeadServer)(nil).CreateLead), arg0, arg1)
}

// MockUnsafeLeadServer is a mock of UnsafeLeadServer interface.
type MockUnsafeLeadServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeLeadServerMockRecorder
}

// MockUnsafeLeadServerMockRecorder is the mock recorder for MockUnsafeLeadServer.
type MockUnsafeLeadServerMockRecorder struct {
	mock *MockUnsafeLeadServer
}

// NewMockUnsafeLeadServer creates a new mock instance.
func NewMockUnsafeLeadServer(ctrl *gomock.Controller) *MockUnsafeLeadServer {
	mock := &MockUnsafeLeadServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeLeadServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeLeadServer) EXPECT() *MockUnsafeLeadServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedLeadServer mocks base method.
func (m *MockUnsafeLeadServer) mustEmbedUnimplementedLeadServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedLeadServer")
}

// mustEmbedUnimplementedLeadServer indicates an expected call of mustEmbedUnimplementedLeadServer.
func (mr *MockUnsafeLeadServerMockRecorder) mustEmbedUnimplementedLeadServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedLeadServer", reflect.TypeOf((*MockUnsafeLeadServer)(nil).mustEmbedUnimplementedLeadServer))
}
