// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/sgkyc/internal/okyc.proto

package sgkyc

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// OKYCPayload contains data fetched from OKYC verification.
type OKYCPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PersonalData *OKYCPersonalDetails `protobuf:"bytes,1,opt,name=personal_data,json=personalData,proto3" json:"personal_data,omitempty"`
	AadhaarXml   string               `protobuf:"bytes,2,opt,name=aadhaar_xml,json=aadhaarXml,proto3" json:"aadhaar_xml,omitempty"`
	UserSelfie   []byte               `protobuf:"bytes,3,opt,name=user_selfie,json=userSelfie,proto3" json:"user_selfie,omitempty"`
	AadhaarPhoto []byte               `protobuf:"bytes,4,opt,name=aadhaar_photo,json=aadhaarPhoto,proto3" json:"aadhaar_photo,omitempty"`
}

func (x *OKYCPayload) Reset() {
	*x = OKYCPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgkyc_internal_okyc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OKYCPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OKYCPayload) ProtoMessage() {}

func (x *OKYCPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgkyc_internal_okyc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OKYCPayload.ProtoReflect.Descriptor instead.
func (*OKYCPayload) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgkyc_internal_okyc_proto_rawDescGZIP(), []int{0}
}

func (x *OKYCPayload) GetPersonalData() *OKYCPersonalDetails {
	if x != nil {
		return x.PersonalData
	}
	return nil
}

func (x *OKYCPayload) GetAadhaarXml() string {
	if x != nil {
		return x.AadhaarXml
	}
	return ""
}

func (x *OKYCPayload) GetUserSelfie() []byte {
	if x != nil {
		return x.UserSelfie
	}
	return nil
}

func (x *OKYCPayload) GetAadhaarPhoto() []byte {
	if x != nil {
		return x.AadhaarPhoto
	}
	return nil
}

// OKYCPersonalDetails contains personal information extracted from Aadhaar during OKYC.
type OKYCPersonalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaskedAadhaar string                `protobuf:"bytes,1,opt,name=masked_aadhaar,json=maskedAadhaar,proto3" json:"masked_aadhaar,omitempty"`
	Name          *common.Name          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Gender        common.Gender         `protobuf:"varint,3,opt,name=gender,proto3,enum=api.typesv2.common.Gender" json:"gender,omitempty"`
	Dob           *date.Date            `protobuf:"bytes,4,opt,name=dob,proto3" json:"dob,omitempty"`
	Address       *common.PostalAddress `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *OKYCPersonalDetails) Reset() {
	*x = OKYCPersonalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgkyc_internal_okyc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OKYCPersonalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OKYCPersonalDetails) ProtoMessage() {}

func (x *OKYCPersonalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgkyc_internal_okyc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OKYCPersonalDetails.ProtoReflect.Descriptor instead.
func (*OKYCPersonalDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgkyc_internal_okyc_proto_rawDescGZIP(), []int{1}
}

func (x *OKYCPersonalDetails) GetMaskedAadhaar() string {
	if x != nil {
		return x.MaskedAadhaar
	}
	return ""
}

func (x *OKYCPersonalDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *OKYCPersonalDetails) GetGender() common.Gender {
	if x != nil {
		return x.Gender
	}
	return common.Gender(0)
}

func (x *OKYCPersonalDetails) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *OKYCPersonalDetails) GetAddress() *common.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

var File_api_stockguardian_sgkyc_internal_okyc_proto protoreflect.FileDescriptor

var file_api_stockguardian_sgkyc_internal_okyc_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x6b, 0x79, 0x63, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x6f, 0x6b, 0x79, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x6b,
	0x79, 0x63, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x01, 0x0a,
	0x0b, 0x4f, 0x4b, 0x59, 0x43, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x4d, 0x0a, 0x0d,
	0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x6b, 0x79, 0x63, 0x2e, 0x4f, 0x4b, 0x59, 0x43, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x70,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x78, 0x6d, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x58, 0x6d, 0x6c, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x6c, 0x66, 0x69, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x66, 0x69, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x50, 0x68, 0x6f,
	0x74, 0x6f, 0x22, 0x80, 0x02, 0x0a, 0x13, 0x4f, 0x4b, 0x59, 0x43, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61,
	0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61,
	0x72, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x32, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x3b, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x33, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f,
	0x74, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x6b, 0x79, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_stockguardian_sgkyc_internal_okyc_proto_rawDescOnce sync.Once
	file_api_stockguardian_sgkyc_internal_okyc_proto_rawDescData = file_api_stockguardian_sgkyc_internal_okyc_proto_rawDesc
)

func file_api_stockguardian_sgkyc_internal_okyc_proto_rawDescGZIP() []byte {
	file_api_stockguardian_sgkyc_internal_okyc_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_sgkyc_internal_okyc_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_sgkyc_internal_okyc_proto_rawDescData)
	})
	return file_api_stockguardian_sgkyc_internal_okyc_proto_rawDescData
}

var file_api_stockguardian_sgkyc_internal_okyc_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_stockguardian_sgkyc_internal_okyc_proto_goTypes = []interface{}{
	(*OKYCPayload)(nil),          // 0: stockguardian.sgkyc.OKYCPayload
	(*OKYCPersonalDetails)(nil),  // 1: stockguardian.sgkyc.OKYCPersonalDetails
	(*common.Name)(nil),          // 2: api.typesv2.common.Name
	(common.Gender)(0),           // 3: api.typesv2.common.Gender
	(*date.Date)(nil),            // 4: google.type.Date
	(*common.PostalAddress)(nil), // 5: api.typesv2.common.PostalAddress
}
var file_api_stockguardian_sgkyc_internal_okyc_proto_depIdxs = []int32{
	1, // 0: stockguardian.sgkyc.OKYCPayload.personal_data:type_name -> stockguardian.sgkyc.OKYCPersonalDetails
	2, // 1: stockguardian.sgkyc.OKYCPersonalDetails.name:type_name -> api.typesv2.common.Name
	3, // 2: stockguardian.sgkyc.OKYCPersonalDetails.gender:type_name -> api.typesv2.common.Gender
	4, // 3: stockguardian.sgkyc.OKYCPersonalDetails.dob:type_name -> google.type.Date
	5, // 4: stockguardian.sgkyc.OKYCPersonalDetails.address:type_name -> api.typesv2.common.PostalAddress
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_stockguardian_sgkyc_internal_okyc_proto_init() }
func file_api_stockguardian_sgkyc_internal_okyc_proto_init() {
	if File_api_stockguardian_sgkyc_internal_okyc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_sgkyc_internal_okyc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OKYCPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgkyc_internal_okyc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OKYCPersonalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_sgkyc_internal_okyc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_stockguardian_sgkyc_internal_okyc_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_sgkyc_internal_okyc_proto_depIdxs,
		MessageInfos:      file_api_stockguardian_sgkyc_internal_okyc_proto_msgTypes,
	}.Build()
	File_api_stockguardian_sgkyc_internal_okyc_proto = out.File
	file_api_stockguardian_sgkyc_internal_okyc_proto_rawDesc = nil
	file_api_stockguardian_sgkyc_internal_okyc_proto_goTypes = nil
	file_api_stockguardian_sgkyc_internal_okyc_proto_depIdxs = nil
}
