//go:generate gen_sql -types=CardRequest,CardRequestDetails,CardRequestStageDetails,CardOnboardingRequestDetails,StageInfo
syntax = "proto3";

package api.firefly.v2;

import "google/protobuf/timestamp.proto";
import "api/typesv2/firefly.proto";
import "api/firefly/v2/enums/enums.proto";
import "api/vendorgateway/vendor.proto";


option go_package = "github.com/epifi/gamma/api/firefly/v2";
option java_package = "com.github.epifi.gamma.api.firefly.v2";

message CardRequest {
  string id = 1;
  string actor_id = 2;
  enums.CardRequestType type = 3;
  enums.CardRequestStatus status = 4;
  CardRequestDetails request_details = 5;
  CardRequestStageDetails stage_details = 6;
  vendorgateway.Vendor vendor = 7;
  string external_user_id = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp deleted_at = 11;
}

message CardRequestDetails {
  oneof Data {
    CardOnboardingRequestDetails onboarding_request_details = 1;
    CardDeliveryTrackingDetails card_delivery_tracking_details = 2;
  }
}

message CardDeliveryTrackingDetails {
  // Unique identifier for a each tracking request at Shipway
  string order_id = 1;
  // awb number of the tracking request
  string awb = 2;
  // carrier partner of the tracking request (DELHIVERY, BLUEDART etc)
  string carrier = 3;
  // delivery state of the shipment
  enums.CardTrackingDeliveryState delivery_state = 4;
  // A package is scanned multiple times throughout the journey. On every scan,
  // there is an update in tracking information. Storing information for all the scans.
  repeated Scan scans = 5;
  google.protobuf.Timestamp pickup_date = 6;
  // time at which tracking details such as awb and courier partner are received
  google.protobuf.Timestamp uploaded_at = 7;
  vendorgateway.Vendor printing_vendor = 8;
  // Expected date of delivery
  google.protobuf.Timestamp expected_delivery_date = 9;
  string tracking_url = 10;
}

message Scan {
  string location = 1;
  google.protobuf.Timestamp updated_at = 2;
  string status_description = 3;
}

message CardOnboardingRequestDetails {
  string offer_id = 1;
  // contains the card program used for onboarding
  api.typesv2.CardProgram card_program = 2;
  string vendor_workflow_id = 3;
  api.firefly.v2.enums.CreditCardApplicantType applicant_type = 4;
}

message CardRequestStageDetails {
  // Details of each stage in onboarding process
  // This will contain the history of all the stages - PAN_CHECK, EKYC etc.
  // The key for the map is Stage.string()
  map<string, StageInfo> stages = 1;
}

// Message contains required information of a single stage
message StageInfo {
  // The state of current stage - Initiated, InProgress, Success etc.
  enums.CardRequestStageState state = 1;
  // The timestamp at which the stage was updated with the state.
  // This information will be useful for debugging in case of failures,
  // and to analyze which stages have taken longer to completion etc.
  google.protobuf.Timestamp last_updated_at = 2;
  // The timestamp at which the stage was deemed to be started.
  google.protobuf.Timestamp started_at = 3;
}
