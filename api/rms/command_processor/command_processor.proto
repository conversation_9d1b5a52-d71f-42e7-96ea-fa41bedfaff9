// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.rms.command_processor;

import "google/protobuf/timestamp.proto";
import "api/rms/orchestrator/event/event.proto";
import "api/rpc/status.proto";
import "api/rms/manager/rule.proto";

option go_package = "github.com/epifi/gamma/api/rms/command_processor";
option java_package = "com.github.epifi.gamma.api.rms.command_processor";

/*
A Rule is defined as a set of commands
RMS defines two types of commands, each evaluated by their respective processors:
1. Condition command: the conditions/constraints to be evaluated. The result is a boolean. If true, further processing of the rule takes place. If false, further commands are not executed.
A conditional command processor parses the conditional expression and evaluates them to true/false.

2. Action command: the action to be performed for the rule. For example: "add Rs 5 to SD", "send SMS to user" etc
Action commands are also represented using special expressions which are evaluated using Action command processor. An action is evaluated only if the conditional command processor returns success. Please note: an action command processor only decides on the action to be executed and doesn't execute them. The action is communicated to the client (eg: FiTTT, Rewards etc) which take care of action execution.
*/
service CommandProcessor{
  // Command execution include
  // 1. collecting rules data (conditions, actions defined...)
  // 2. extracting expression parameter values from event, user defined values or by invoking domain APIs
  // 3. condition expression evaluation
  // 4. action processing and delegation to client service
  rpc ExecuteCommands(ExecuteCommandsRequest) returns (ExecuteCommandsResponse){}
}
// RuleExecutionState defines all the possible states of a rule under execution
// state with Suffix as INITIATED should only exist in cases where rule execution is under progress
// if rule execution has competed, it is expected to be either in FAILED or SUCCESS
enum RuleExecutionState{
  RULE_EXECUTION_STATE_UNSPECIFIED = 0;
  // pre-processing is completed,  started condition evaluation
  CONDITION_EVALUATION_INITIATED = 3;
  // condition evaluation failed
  CONDITION_EVALUATION_FAILED = 4;
  // condition evaluation success, here two cases may occur
  // 1. condition satisfied : subsequent commands will be executed
  // 2. condition not satisfied : rule execution terminates here, and final state will be set as COMMAND_PROCESSING_SUCCESS, with a descriptive message `condition not satisfied`
  CONDITION_EVALUATION_SUCCESS = 5;
  // condition satisfied, started action processing
  ACTION_PROCESSING_INITIATED = 6;
  // action processing failed
  ACTION_PROCESSING_FAILED = 7;
  // action processing completed
  // in case of multiple actions in a rule, ActionProcessingSuccess will happen only upon successful processing & delegation of all the actions
  // action processor to take care either all the actions are processed or none
  ACTION_PROCESSING_SUCCESS = 8;
  // action specific fact is created before condition processor
  // FACT_CONSTRUCTION_FAILED denotes failure in fact creation
  FACT_CONSTRUCTION_FAILED = 9;
  // There can be certain checks to be added before starting rule execution
  // example: deposit amount = 0 in case of Auto save
  // If the checks are not satisfied rule evaluation is not processed further. This is a VALID case
  PRE_EXECUTION_CHECKS_NOT_SATISFIED = 10;
  // for app usage event, we bypass the condition evaluation check in RMS, and request it directly from the client
  // condition evaluation is still not requested from the client
  CONDITION_EVALUATION_AT_CLIENT_PENDING = 11;
  // request to client for condition evaluation is queued
  CONDITION_EVALUATION_AT_CLIENT_QUEUED = 12;
  // client has provided the condition evaluation results
  CONDITION_EVALUATION_AT_CLIENT_SUCCESS = 13;
  // no internet access at client
  ARCHIVED_DUE_TO_NO_INTERNET_ACCESS_TO_APP = 14;
}

// Command Processor consumes ExecuteCommandsRequest and triggers execution of all the commands defined
message ExecuteCommandsRequest{
  string subscription_id = 1;
  api.rms.orchestrator.event.RmsEvent event = 2;
  api.rms.manager.Rule rule = 3;
  api.rms.manager.RuleSubscription subscription = 4;
}

message ExecuteCommandsResponse{
  rpc.Status status = 1;
  RuleExecutionState execution_state = 2;
  string execution_id = 3;
}

// Rule execution defines state of any rule under execution or which have completed its execution
message RuleExecution{
  string execution_id = 1;
  string subscription_id = 2;
  RuleExecutionState state = 3;
  // description provide little more detail on the last state
  // this would be helpful mainly in cases of FAILURE, error string can be dumped
  string description = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  string client_event_id = 7;
  string subscription_version_id = 8;
  string event_unique_key = 9;
  // This field will be populated if the rule subscriptions are executed in a batch;
  // otherwise, it will remain empty.
  string batch_id = 10;
}
