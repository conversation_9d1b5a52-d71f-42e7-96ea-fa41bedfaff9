// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/chat/bot/workflow/messages.proto

package workflow

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// WorkflowData: abstract proto message for data returned from backend
type WorkflowData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//
	//	*WorkflowData_FaqData
	//	*WorkflowData_UserDetailsData
	//	*WorkflowData_TxnListData
	//	*WorkflowData_TxnDetailsData
	//	*WorkflowData_CreditCardStateData
	//	*WorkflowData_DebitCardTrackingData
	//	*WorkflowData_CreditCardTxnListData
	//	*WorkflowData_EmploymentData
	//	*WorkflowData_ScreenerAttemptsData
	//	*WorkflowData_FetchDisputeData
	//	*WorkflowData_LivenessData
	//	*WorkflowData_FetchRewardOffersForUserData
	//	*WorkflowData_CheckSalaryProgramAmazonVoucherEligibilityData
	//	*WorkflowData_ChargeList
	//	*WorkflowData_DisplayTxnReasonData
	//	*WorkflowData_FailedTxnsData
	//	*WorkflowData_FetchRewardsEventDetailsData
	//	*WorkflowData_FetchSalaryProgramRegistrationDetailsData
	//	*WorkflowData_FetchRewardForEventData
	//	*WorkflowData_ReleaseEvaluatorData
	//	*WorkflowData_PredefinedMessageTemplateData
	//	*WorkflowData_BalanceRefreshData
	//	*WorkflowData_FetchUserTransactionsData
	Data isWorkflowData_Data `protobuf_oneof:"data"`
}

func (x *WorkflowData) Reset() {
	*x = WorkflowData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_bot_workflow_messages_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkflowData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowData) ProtoMessage() {}

func (x *WorkflowData) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_bot_workflow_messages_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowData.ProtoReflect.Descriptor instead.
func (*WorkflowData) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_bot_workflow_messages_proto_rawDescGZIP(), []int{0}
}

func (m *WorkflowData) GetData() isWorkflowData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *WorkflowData) GetFaqData() *FaqData {
	if x, ok := x.GetData().(*WorkflowData_FaqData); ok {
		return x.FaqData
	}
	return nil
}

func (x *WorkflowData) GetUserDetailsData() *UserDetailsData {
	if x, ok := x.GetData().(*WorkflowData_UserDetailsData); ok {
		return x.UserDetailsData
	}
	return nil
}

func (x *WorkflowData) GetTxnListData() *TxnListData {
	if x, ok := x.GetData().(*WorkflowData_TxnListData); ok {
		return x.TxnListData
	}
	return nil
}

func (x *WorkflowData) GetTxnDetailsData() *TxnDetailsData {
	if x, ok := x.GetData().(*WorkflowData_TxnDetailsData); ok {
		return x.TxnDetailsData
	}
	return nil
}

func (x *WorkflowData) GetCreditCardStateData() *CreditCardStateData {
	if x, ok := x.GetData().(*WorkflowData_CreditCardStateData); ok {
		return x.CreditCardStateData
	}
	return nil
}

func (x *WorkflowData) GetDebitCardTrackingData() *DebitCardTrackingData {
	if x, ok := x.GetData().(*WorkflowData_DebitCardTrackingData); ok {
		return x.DebitCardTrackingData
	}
	return nil
}

func (x *WorkflowData) GetCreditCardTxnListData() *CreditCardTxnListData {
	if x, ok := x.GetData().(*WorkflowData_CreditCardTxnListData); ok {
		return x.CreditCardTxnListData
	}
	return nil
}

func (x *WorkflowData) GetEmploymentData() *EmploymentData {
	if x, ok := x.GetData().(*WorkflowData_EmploymentData); ok {
		return x.EmploymentData
	}
	return nil
}

func (x *WorkflowData) GetScreenerAttemptsData() *ScreenerAttemptsData {
	if x, ok := x.GetData().(*WorkflowData_ScreenerAttemptsData); ok {
		return x.ScreenerAttemptsData
	}
	return nil
}

func (x *WorkflowData) GetFetchDisputeData() *FetchDisputeData {
	if x, ok := x.GetData().(*WorkflowData_FetchDisputeData); ok {
		return x.FetchDisputeData
	}
	return nil
}

func (x *WorkflowData) GetLivenessData() *LivenessData {
	if x, ok := x.GetData().(*WorkflowData_LivenessData); ok {
		return x.LivenessData
	}
	return nil
}

func (x *WorkflowData) GetFetchRewardOffersForUserData() *FetchRewardOffersForUserData {
	if x, ok := x.GetData().(*WorkflowData_FetchRewardOffersForUserData); ok {
		return x.FetchRewardOffersForUserData
	}
	return nil
}

func (x *WorkflowData) GetCheckSalaryProgramAmazonVoucherEligibilityData() *CheckSalaryProgramAmazonVoucherEligibilityData {
	if x, ok := x.GetData().(*WorkflowData_CheckSalaryProgramAmazonVoucherEligibilityData); ok {
		return x.CheckSalaryProgramAmazonVoucherEligibilityData
	}
	return nil
}

func (x *WorkflowData) GetChargeList() *ChargesListForActor {
	if x, ok := x.GetData().(*WorkflowData_ChargeList); ok {
		return x.ChargeList
	}
	return nil
}

func (x *WorkflowData) GetDisplayTxnReasonData() *DisplayTxnReasonData {
	if x, ok := x.GetData().(*WorkflowData_DisplayTxnReasonData); ok {
		return x.DisplayTxnReasonData
	}
	return nil
}

func (x *WorkflowData) GetFailedTxnsData() *FailedTxnsData {
	if x, ok := x.GetData().(*WorkflowData_FailedTxnsData); ok {
		return x.FailedTxnsData
	}
	return nil
}

func (x *WorkflowData) GetFetchRewardsEventDetailsData() *FetchRewardsEventDetailsData {
	if x, ok := x.GetData().(*WorkflowData_FetchRewardsEventDetailsData); ok {
		return x.FetchRewardsEventDetailsData
	}
	return nil
}

func (x *WorkflowData) GetFetchSalaryProgramRegistrationDetailsData() *FetchSalaryProgramRegistrationDetailsData {
	if x, ok := x.GetData().(*WorkflowData_FetchSalaryProgramRegistrationDetailsData); ok {
		return x.FetchSalaryProgramRegistrationDetailsData
	}
	return nil
}

func (x *WorkflowData) GetFetchRewardForEventData() *FetchRewardForEventData {
	if x, ok := x.GetData().(*WorkflowData_FetchRewardForEventData); ok {
		return x.FetchRewardForEventData
	}
	return nil
}

func (x *WorkflowData) GetReleaseEvaluatorData() *ReleaseEvaluatorData {
	if x, ok := x.GetData().(*WorkflowData_ReleaseEvaluatorData); ok {
		return x.ReleaseEvaluatorData
	}
	return nil
}

func (x *WorkflowData) GetPredefinedMessageTemplateData() *PredefinedMessageTemplateData {
	if x, ok := x.GetData().(*WorkflowData_PredefinedMessageTemplateData); ok {
		return x.PredefinedMessageTemplateData
	}
	return nil
}

func (x *WorkflowData) GetBalanceRefreshData() *BalanceRefreshData {
	if x, ok := x.GetData().(*WorkflowData_BalanceRefreshData); ok {
		return x.BalanceRefreshData
	}
	return nil
}

func (x *WorkflowData) GetFetchUserTransactionsData() *FetchUserTransactionsData {
	if x, ok := x.GetData().(*WorkflowData_FetchUserTransactionsData); ok {
		return x.FetchUserTransactionsData
	}
	return nil
}

type isWorkflowData_Data interface {
	isWorkflowData_Data()
}

type WorkflowData_FaqData struct {
	// faq data (list of article objects)
	FaqData *FaqData `protobuf:"bytes,1,opt,name=faq_data,json=faqData,proto3,oneof"`
}

type WorkflowData_UserDetailsData struct {
	// relevant user details data returned by the API
	UserDetailsData *UserDetailsData `protobuf:"bytes,2,opt,name=user_details_data,json=userDetailsData,proto3,oneof"`
}

type WorkflowData_TxnListData struct {
	// the transactions list data returned by the API
	TxnListData *TxnListData `protobuf:"bytes,3,opt,name=txn_list_data,json=txnListData,proto3,oneof"`
}

type WorkflowData_TxnDetailsData struct {
	// the transactions details data returned by the API
	TxnDetailsData *TxnDetailsData `protobuf:"bytes,4,opt,name=txn_details_data,json=txnDetailsData,proto3,oneof"`
}

type WorkflowData_CreditCardStateData struct {
	// credit card state data returned by the API
	CreditCardStateData *CreditCardStateData `protobuf:"bytes,5,opt,name=credit_card_state_data,json=creditCardStateData,proto3,oneof"`
}

type WorkflowData_DebitCardTrackingData struct {
	// debit card tracking details data returned by the API
	DebitCardTrackingData *DebitCardTrackingData `protobuf:"bytes,6,opt,name=debit_card_tracking_data,json=debitCardTrackingData,proto3,oneof"`
}

type WorkflowData_CreditCardTxnListData struct {
	// credit card transactions list data returned by the API
	CreditCardTxnListData *CreditCardTxnListData `protobuf:"bytes,7,opt,name=credit_card_txn_list_data,json=creditCardTxnListData,proto3,oneof"`
}

type WorkflowData_EmploymentData struct {
	// employment data returned by the API
	EmploymentData *EmploymentData `protobuf:"bytes,8,opt,name=employment_data,json=employmentData,proto3,oneof"`
}

type WorkflowData_ScreenerAttemptsData struct {
	// screener attempts data returned by the API
	ScreenerAttemptsData *ScreenerAttemptsData `protobuf:"bytes,9,opt,name=screener_attempts_data,json=screenerAttemptsData,proto3,oneof"`
}

type WorkflowData_FetchDisputeData struct {
	// dispute details for the given txn id if it exists
	FetchDisputeData *FetchDisputeData `protobuf:"bytes,10,opt,name=fetch_dispute_data,json=fetchDisputeData,proto3,oneof"`
}

type WorkflowData_LivenessData struct {
	// liveness details for the actor
	LivenessData *LivenessData `protobuf:"bytes,11,opt,name=liveness_data,json=livenessData,proto3,oneof"`
}

type WorkflowData_FetchRewardOffersForUserData struct {
	// reward offers applicable to user
	FetchRewardOffersForUserData *FetchRewardOffersForUserData `protobuf:"bytes,12,opt,name=fetch_reward_offers_for_user_data,json=fetchRewardOffersForUserData,proto3,oneof"`
}

type WorkflowData_CheckSalaryProgramAmazonVoucherEligibilityData struct {
	// eligibility for salary program amazon voucher
	CheckSalaryProgramAmazonVoucherEligibilityData *CheckSalaryProgramAmazonVoucherEligibilityData `protobuf:"bytes,13,opt,name=check_salary_program_amazon_voucher_eligibility_data,json=checkSalaryProgramAmazonVoucherEligibilityData,proto3,oneof"`
}

type WorkflowData_ChargeList struct {
	// list of debited charge transactions returned by the API
	ChargeList *ChargesListForActor `protobuf:"bytes,14,opt,name=charge_list,json=chargeList,proto3,oneof"`
}

type WorkflowData_DisplayTxnReasonData struct {
	// reason for each txn returned by the API
	DisplayTxnReasonData *DisplayTxnReasonData `protobuf:"bytes,15,opt,name=display_txn_reason_data,json=displayTxnReasonData,proto3,oneof"`
}

type WorkflowData_FailedTxnsData struct {
	// list of failed transactions returned by the API
	FailedTxnsData *FailedTxnsData `protobuf:"bytes,16,opt,name=failed_txns_data,json=failedTxnsData,proto3,oneof"`
}

type WorkflowData_FetchRewardsEventDetailsData struct {
	// fetches reward event details
	FetchRewardsEventDetailsData *FetchRewardsEventDetailsData `protobuf:"bytes,17,opt,name=fetch_rewards_event_details_data,json=fetchRewardsEventDetailsData,proto3,oneof"`
}

type WorkflowData_FetchSalaryProgramRegistrationDetailsData struct {
	// salary program registration stage status details
	FetchSalaryProgramRegistrationDetailsData *FetchSalaryProgramRegistrationDetailsData `protobuf:"bytes,18,opt,name=fetch_salary_program_registration_details_data,json=fetchSalaryProgramRegistrationDetailsData,proto3,oneof"`
}

type WorkflowData_FetchRewardForEventData struct {
	// reward details for a particular event
	FetchRewardForEventData *FetchRewardForEventData `protobuf:"bytes,19,opt,name=fetch_reward_for_event_data,json=fetchRewardForEventData,proto3,oneof"`
}

type WorkflowData_ReleaseEvaluatorData struct {
	// Data representing the whether the chatbot is released for the user
	ReleaseEvaluatorData *ReleaseEvaluatorData `protobuf:"bytes,20,opt,name=release_evaluator_data,json=releaseEvaluatorData,proto3,oneof"`
}

type WorkflowData_PredefinedMessageTemplateData struct {
	// Data representing the predefined message to be displayed to the user (if any)
	PredefinedMessageTemplateData *PredefinedMessageTemplateData `protobuf:"bytes,21,opt,name=predefined_message_template_data,json=predefinedMessageTemplateData,proto3,oneof"`
}

type WorkflowData_BalanceRefreshData struct {
	// refreshed balance for the user
	BalanceRefreshData *BalanceRefreshData `protobuf:"bytes,22,opt,name=balance_refresh_data,json=balanceRefreshData,proto3,oneof"`
}

type WorkflowData_FetchUserTransactionsData struct {
	// data for the transactions of the user
	FetchUserTransactionsData *FetchUserTransactionsData `protobuf:"bytes,23,opt,name=fetch_user_transactions_data,json=fetchUserTransactionsData,proto3,oneof"`
}

func (*WorkflowData_FaqData) isWorkflowData_Data() {}

func (*WorkflowData_UserDetailsData) isWorkflowData_Data() {}

func (*WorkflowData_TxnListData) isWorkflowData_Data() {}

func (*WorkflowData_TxnDetailsData) isWorkflowData_Data() {}

func (*WorkflowData_CreditCardStateData) isWorkflowData_Data() {}

func (*WorkflowData_DebitCardTrackingData) isWorkflowData_Data() {}

func (*WorkflowData_CreditCardTxnListData) isWorkflowData_Data() {}

func (*WorkflowData_EmploymentData) isWorkflowData_Data() {}

func (*WorkflowData_ScreenerAttemptsData) isWorkflowData_Data() {}

func (*WorkflowData_FetchDisputeData) isWorkflowData_Data() {}

func (*WorkflowData_LivenessData) isWorkflowData_Data() {}

func (*WorkflowData_FetchRewardOffersForUserData) isWorkflowData_Data() {}

func (*WorkflowData_CheckSalaryProgramAmazonVoucherEligibilityData) isWorkflowData_Data() {}

func (*WorkflowData_ChargeList) isWorkflowData_Data() {}

func (*WorkflowData_DisplayTxnReasonData) isWorkflowData_Data() {}

func (*WorkflowData_FailedTxnsData) isWorkflowData_Data() {}

func (*WorkflowData_FetchRewardsEventDetailsData) isWorkflowData_Data() {}

func (*WorkflowData_FetchSalaryProgramRegistrationDetailsData) isWorkflowData_Data() {}

func (*WorkflowData_FetchRewardForEventData) isWorkflowData_Data() {}

func (*WorkflowData_ReleaseEvaluatorData) isWorkflowData_Data() {}

func (*WorkflowData_PredefinedMessageTemplateData) isWorkflowData_Data() {}

func (*WorkflowData_BalanceRefreshData) isWorkflowData_Data() {}

func (*WorkflowData_FetchUserTransactionsData) isWorkflowData_Data() {}

// FetchDataParameters: specifies params to backend on which data has to be fetched
type FetchDataParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to EntityParameters:
	//
	//	*FetchDataParameters_FaqParameters
	//	*FetchDataParameters_UserDetailsParameters
	//	*FetchDataParameters_TxnListParameters
	//	*FetchDataParameters_TxnDetailsParameters
	//	*FetchDataParameters_CreditCardStateParameters
	//	*FetchDataParameters_DebitCardTrackingParameters
	//	*FetchDataParameters_CreditCardTxnListParameters
	//	*FetchDataParameters_EmploymentDataParameters
	//	*FetchDataParameters_ScreenerAttemptsDataParameters
	//	*FetchDataParameters_DisputeParameters
	//	*FetchDataParameters_LivenessDataParams
	//	*FetchDataParameters_FetchRewardOffersForUserParameters
	//	*FetchDataParameters_CheckSalaryProgramAmazonVoucherEligibilityParams
	//	*FetchDataParameters_ChargesListForActorParams
	//	*FetchDataParameters_DisplayTxnReasonParams
	//	*FetchDataParameters_FailedTxnsDataParams
	//	*FetchDataParameters_FetchRewardsEventDetailsParameters
	//	*FetchDataParameters_FetchSalaryProgramRegistrationDetailsParameters
	//	*FetchDataParameters_FetchRewardForEventParameters
	//	*FetchDataParameters_ReleaseEvaluatorParameters
	//	*FetchDataParameters_PredefinedMessageTemplateParameters
	//	*FetchDataParameters_BalanceRefreshParams
	//	*FetchDataParameters_FetchUserTransactionsParameters
	EntityParameters isFetchDataParameters_EntityParameters `protobuf_oneof:"entity_parameters"`
}

func (x *FetchDataParameters) Reset() {
	*x = FetchDataParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_bot_workflow_messages_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDataParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDataParameters) ProtoMessage() {}

func (x *FetchDataParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_bot_workflow_messages_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDataParameters.ProtoReflect.Descriptor instead.
func (*FetchDataParameters) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_bot_workflow_messages_proto_rawDescGZIP(), []int{1}
}

func (m *FetchDataParameters) GetEntityParameters() isFetchDataParameters_EntityParameters {
	if m != nil {
		return m.EntityParameters
	}
	return nil
}

func (x *FetchDataParameters) GetFaqParameters() *FaqParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_FaqParameters); ok {
		return x.FaqParameters
	}
	return nil
}

func (x *FetchDataParameters) GetUserDetailsParameters() *UserDetailsParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_UserDetailsParameters); ok {
		return x.UserDetailsParameters
	}
	return nil
}

func (x *FetchDataParameters) GetTxnListParameters() *TxnListParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_TxnListParameters); ok {
		return x.TxnListParameters
	}
	return nil
}

func (x *FetchDataParameters) GetTxnDetailsParameters() *TxnDetailsParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_TxnDetailsParameters); ok {
		return x.TxnDetailsParameters
	}
	return nil
}

func (x *FetchDataParameters) GetCreditCardStateParameters() *CreditCardStateParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_CreditCardStateParameters); ok {
		return x.CreditCardStateParameters
	}
	return nil
}

func (x *FetchDataParameters) GetDebitCardTrackingParameters() *DebitCardTrackingParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_DebitCardTrackingParameters); ok {
		return x.DebitCardTrackingParameters
	}
	return nil
}

func (x *FetchDataParameters) GetCreditCardTxnListParameters() *CreditCardTxnListParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_CreditCardTxnListParameters); ok {
		return x.CreditCardTxnListParameters
	}
	return nil
}

func (x *FetchDataParameters) GetEmploymentDataParameters() *EmploymentDataParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_EmploymentDataParameters); ok {
		return x.EmploymentDataParameters
	}
	return nil
}

func (x *FetchDataParameters) GetScreenerAttemptsDataParameters() *ScreenerAttemptsDataParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_ScreenerAttemptsDataParameters); ok {
		return x.ScreenerAttemptsDataParameters
	}
	return nil
}

func (x *FetchDataParameters) GetDisputeParameters() *DisputeParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_DisputeParameters); ok {
		return x.DisputeParameters
	}
	return nil
}

func (x *FetchDataParameters) GetLivenessDataParams() *LivenessDataParams {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_LivenessDataParams); ok {
		return x.LivenessDataParams
	}
	return nil
}

func (x *FetchDataParameters) GetFetchRewardOffersForUserParameters() *FetchRewardOffersForUserParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_FetchRewardOffersForUserParameters); ok {
		return x.FetchRewardOffersForUserParameters
	}
	return nil
}

func (x *FetchDataParameters) GetCheckSalaryProgramAmazonVoucherEligibilityParams() *CheckSalaryProgramAmazonVoucherEligibilityParams {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_CheckSalaryProgramAmazonVoucherEligibilityParams); ok {
		return x.CheckSalaryProgramAmazonVoucherEligibilityParams
	}
	return nil
}

func (x *FetchDataParameters) GetChargesListForActorParams() *ChargesListForActorParams {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_ChargesListForActorParams); ok {
		return x.ChargesListForActorParams
	}
	return nil
}

func (x *FetchDataParameters) GetDisplayTxnReasonParams() *DisplayTxnReasonParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_DisplayTxnReasonParams); ok {
		return x.DisplayTxnReasonParams
	}
	return nil
}

func (x *FetchDataParameters) GetFailedTxnsDataParams() *FailedTxnsDataParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_FailedTxnsDataParams); ok {
		return x.FailedTxnsDataParams
	}
	return nil
}

func (x *FetchDataParameters) GetFetchRewardsEventDetailsParameters() *FetchRewardsEventDetailsParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_FetchRewardsEventDetailsParameters); ok {
		return x.FetchRewardsEventDetailsParameters
	}
	return nil
}

func (x *FetchDataParameters) GetFetchSalaryProgramRegistrationDetailsParameters() *FetchSalaryProgramRegistrationDetailsParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_FetchSalaryProgramRegistrationDetailsParameters); ok {
		return x.FetchSalaryProgramRegistrationDetailsParameters
	}
	return nil
}

func (x *FetchDataParameters) GetFetchRewardForEventParameters() *FetchRewardForEventParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_FetchRewardForEventParameters); ok {
		return x.FetchRewardForEventParameters
	}
	return nil
}

func (x *FetchDataParameters) GetReleaseEvaluatorParameters() *ReleaseEvaluatorDataParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_ReleaseEvaluatorParameters); ok {
		return x.ReleaseEvaluatorParameters
	}
	return nil
}

func (x *FetchDataParameters) GetPredefinedMessageTemplateParameters() *PredefinedMessageTemplateDataParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_PredefinedMessageTemplateParameters); ok {
		return x.PredefinedMessageTemplateParameters
	}
	return nil
}

func (x *FetchDataParameters) GetBalanceRefreshParams() *BalanceRefreshParams {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_BalanceRefreshParams); ok {
		return x.BalanceRefreshParams
	}
	return nil
}

func (x *FetchDataParameters) GetFetchUserTransactionsParameters() *FetchUserTransactionsParameters {
	if x, ok := x.GetEntityParameters().(*FetchDataParameters_FetchUserTransactionsParameters); ok {
		return x.FetchUserTransactionsParameters
	}
	return nil
}

type isFetchDataParameters_EntityParameters interface {
	isFetchDataParameters_EntityParameters()
}

type FetchDataParameters_FaqParameters struct {
	// faq params: type, context, etc
	FaqParameters *FaqParameters `protobuf:"bytes,1,opt,name=faq_parameters,json=faqParameters,proto3,oneof"`
}

type FetchDataParameters_UserDetailsParameters struct {
	// user details params
	UserDetailsParameters *UserDetailsParameters `protobuf:"bytes,2,opt,name=user_details_parameters,json=userDetailsParameters,proto3,oneof"`
}

type FetchDataParameters_TxnListParameters struct {
	// params required for fetching transactions list
	TxnListParameters *TxnListParameters `protobuf:"bytes,3,opt,name=txn_list_parameters,json=txnListParameters,proto3,oneof"`
}

type FetchDataParameters_TxnDetailsParameters struct {
	// params required for fetching details of a single transaction
	TxnDetailsParameters *TxnDetailsParameters `protobuf:"bytes,4,opt,name=txn_details_parameters,json=txnDetailsParameters,proto3,oneof"`
}

type FetchDataParameters_CreditCardStateParameters struct {
	// params required for fetching credit card state
	CreditCardStateParameters *CreditCardStateParameters `protobuf:"bytes,5,opt,name=credit_card_state_parameters,json=creditCardStateParameters,proto3,oneof"`
}

type FetchDataParameters_DebitCardTrackingParameters struct {
	// params required for fetching tracking details for a debit card
	DebitCardTrackingParameters *DebitCardTrackingParameters `protobuf:"bytes,6,opt,name=debit_card_tracking_parameters,json=debitCardTrackingParameters,proto3,oneof"`
}

type FetchDataParameters_CreditCardTxnListParameters struct {
	// params required for fetching credit card transactions list
	CreditCardTxnListParameters *CreditCardTxnListParameters `protobuf:"bytes,7,opt,name=credit_card_txn_list_parameters,json=creditCardTxnListParameters,proto3,oneof"`
}

type FetchDataParameters_EmploymentDataParameters struct {
	// params required for fetching employment data
	EmploymentDataParameters *EmploymentDataParameters `protobuf:"bytes,8,opt,name=employment_data_parameters,json=employmentDataParameters,proto3,oneof"`
}

type FetchDataParameters_ScreenerAttemptsDataParameters struct {
	// params required for fetching screener attempts data
	ScreenerAttemptsDataParameters *ScreenerAttemptsDataParameters `protobuf:"bytes,9,opt,name=screener_attempts_data_parameters,json=screenerAttemptsDataParameters,proto3,oneof"`
}

type FetchDataParameters_DisputeParameters struct {
	// params required for fetching dispute details for a transaction
	DisputeParameters *DisputeParameters `protobuf:"bytes,10,opt,name=dispute_parameters,json=disputeParameters,proto3,oneof"`
}

type FetchDataParameters_LivenessDataParams struct {
	// params required for fetching liveness details during onboarding
	LivenessDataParams *LivenessDataParams `protobuf:"bytes,11,opt,name=liveness_data_params,json=livenessDataParams,proto3,oneof"`
}

type FetchDataParameters_FetchRewardOffersForUserParameters struct {
	// params required for FetchRewardOffersForUserData
	FetchRewardOffersForUserParameters *FetchRewardOffersForUserParameters `protobuf:"bytes,12,opt,name=fetch_reward_offers_for_user_parameters,json=fetchRewardOffersForUserParameters,proto3,oneof"`
}

type FetchDataParameters_CheckSalaryProgramAmazonVoucherEligibilityParams struct {
	// params to check the eligibility for salary program amazon voucher
	CheckSalaryProgramAmazonVoucherEligibilityParams *CheckSalaryProgramAmazonVoucherEligibilityParams `protobuf:"bytes,13,opt,name=check_salary_program_amazon_voucher_eligibility_params,json=checkSalaryProgramAmazonVoucherEligibilityParams,proto3,oneof"`
}

type FetchDataParameters_ChargesListForActorParams struct {
	// params required to fetch charges debited for an actor.
	ChargesListForActorParams *ChargesListForActorParams `protobuf:"bytes,14,opt,name=charges_list_for_actor_params,json=chargesListForActorParams,proto3,oneof"`
}

type FetchDataParameters_DisplayTxnReasonParams struct {
	// params required to display reasons for transactions
	DisplayTxnReasonParams *DisplayTxnReasonParameters `protobuf:"bytes,15,opt,name=display_txn_reason_params,json=displayTxnReasonParams,proto3,oneof"`
}

type FetchDataParameters_FailedTxnsDataParams struct {
	// params required to fetch failed transactions for an actor
	FailedTxnsDataParams *FailedTxnsDataParameters `protobuf:"bytes,16,opt,name=failed_txns_data_params,json=failedTxnsDataParams,proto3,oneof"`
}

type FetchDataParameters_FetchRewardsEventDetailsParameters struct {
	// params required for FetchEventDetailsData
	FetchRewardsEventDetailsParameters *FetchRewardsEventDetailsParameters `protobuf:"bytes,17,opt,name=fetch_rewards_event_details_parameters,json=fetchRewardsEventDetailsParameters,proto3,oneof"`
}

type FetchDataParameters_FetchSalaryProgramRegistrationDetailsParameters struct {
	// params required to fetch salary program registration stage status for actor
	FetchSalaryProgramRegistrationDetailsParameters *FetchSalaryProgramRegistrationDetailsParameters `protobuf:"bytes,18,opt,name=fetch_salary_program_registration_details_parameters,json=fetchSalaryProgramRegistrationDetailsParameters,proto3,oneof"`
}

type FetchDataParameters_FetchRewardForEventParameters struct {
	// params required to fetch reward for event
	FetchRewardForEventParameters *FetchRewardForEventParameters `protobuf:"bytes,19,opt,name=fetch_reward_for_event_parameters,json=fetchRewardForEventParameters,proto3,oneof"`
}

type FetchDataParameters_ReleaseEvaluatorParameters struct {
	// params required to fetch whether chatbot feature is enabled for the user
	ReleaseEvaluatorParameters *ReleaseEvaluatorDataParameters `protobuf:"bytes,20,opt,name=release_evaluator_parameters,json=releaseEvaluatorParameters,proto3,oneof"`
}

type FetchDataParameters_PredefinedMessageTemplateParameters struct {
	// params required to fetch predefined messages for eligible users
	PredefinedMessageTemplateParameters *PredefinedMessageTemplateDataParameters `protobuf:"bytes,21,opt,name=predefined_message_template_parameters,json=predefinedMessageTemplateParameters,proto3,oneof"`
}

type FetchDataParameters_BalanceRefreshParams struct {
	// params required to refresh balance and show refreshed balance for user
	BalanceRefreshParams *BalanceRefreshParams `protobuf:"bytes,22,opt,name=balance_refresh_params,json=balanceRefreshParams,proto3,oneof"`
}

type FetchDataParameters_FetchUserTransactionsParameters struct {
	// params to get transaction of user
	FetchUserTransactionsParameters *FetchUserTransactionsParameters `protobuf:"bytes,23,opt,name=fetch_user_transactions_parameters,json=fetchUserTransactionsParameters,proto3,oneof"`
}

func (*FetchDataParameters_FaqParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_UserDetailsParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_TxnListParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_TxnDetailsParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_CreditCardStateParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_DebitCardTrackingParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_CreditCardTxnListParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_EmploymentDataParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_ScreenerAttemptsDataParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_DisputeParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_LivenessDataParams) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_FetchRewardOffersForUserParameters) isFetchDataParameters_EntityParameters() {
}

func (*FetchDataParameters_CheckSalaryProgramAmazonVoucherEligibilityParams) isFetchDataParameters_EntityParameters() {
}

func (*FetchDataParameters_ChargesListForActorParams) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_DisplayTxnReasonParams) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_FailedTxnsDataParams) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_FetchRewardsEventDetailsParameters) isFetchDataParameters_EntityParameters() {
}

func (*FetchDataParameters_FetchSalaryProgramRegistrationDetailsParameters) isFetchDataParameters_EntityParameters() {
}

func (*FetchDataParameters_FetchRewardForEventParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_ReleaseEvaluatorParameters) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_PredefinedMessageTemplateParameters) isFetchDataParameters_EntityParameters() {
}

func (*FetchDataParameters_BalanceRefreshParams) isFetchDataParameters_EntityParameters() {}

func (*FetchDataParameters_FetchUserTransactionsParameters) isFetchDataParameters_EntityParameters() {
}

// ExecuteActionParams: abstract proto message specifying params for task which has to be executed in backend
type ExecuteActionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ActionParams:
	//
	//	*ExecuteActionParams_CreateTicketParams
	ActionParams isExecuteActionParams_ActionParams `protobuf_oneof:"action_params"`
}

func (x *ExecuteActionParams) Reset() {
	*x = ExecuteActionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_bot_workflow_messages_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecuteActionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteActionParams) ProtoMessage() {}

func (x *ExecuteActionParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_bot_workflow_messages_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteActionParams.ProtoReflect.Descriptor instead.
func (*ExecuteActionParams) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_bot_workflow_messages_proto_rawDescGZIP(), []int{2}
}

func (m *ExecuteActionParams) GetActionParams() isExecuteActionParams_ActionParams {
	if m != nil {
		return m.ActionParams
	}
	return nil
}

func (x *ExecuteActionParams) GetCreateTicketParams() *CreateTicketParams {
	if x, ok := x.GetActionParams().(*ExecuteActionParams_CreateTicketParams); ok {
		return x.CreateTicketParams
	}
	return nil
}

type isExecuteActionParams_ActionParams interface {
	isExecuteActionParams_ActionParams()
}

type ExecuteActionParams_CreateTicketParams struct {
	// params for creating a ticket
	CreateTicketParams *CreateTicketParams `protobuf:"bytes,1,opt,name=create_ticket_params,json=createTicketParams,proto3,oneof"`
}

func (*ExecuteActionParams_CreateTicketParams) isExecuteActionParams_ActionParams() {}

// ActionResult: abstract proto message specifying response from backend once task is executed
type ActionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Result:
	//
	//	*ActionResult_CreateTicketResult
	Result isActionResult_Result `protobuf_oneof:"result"`
}

func (x *ActionResult) Reset() {
	*x = ActionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_chat_bot_workflow_messages_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionResult) ProtoMessage() {}

func (x *ActionResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_chat_bot_workflow_messages_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionResult.ProtoReflect.Descriptor instead.
func (*ActionResult) Descriptor() ([]byte, []int) {
	return file_api_cx_chat_bot_workflow_messages_proto_rawDescGZIP(), []int{3}
}

func (m *ActionResult) GetResult() isActionResult_Result {
	if m != nil {
		return m.Result
	}
	return nil
}

func (x *ActionResult) GetCreateTicketResult() *CreateTicketResult {
	if x, ok := x.GetResult().(*ActionResult_CreateTicketResult); ok {
		return x.CreateTicketResult
	}
	return nil
}

type isActionResult_Result interface {
	isActionResult_Result()
}

type ActionResult_CreateTicketResult struct {
	CreateTicketResult *CreateTicketResult `protobuf:"bytes,1,opt,name=create_ticket_result,json=createTicketResult,proto3,oneof"`
}

func (*ActionResult_CreateTicketResult) isActionResult_Result() {}

var File_api_cx_chat_bot_workflow_messages_proto protoreflect.FileDescriptor

var file_api_cx_chat_bot_workflow_messages_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f,
	0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x78, 0x2e, 0x63, 0x68,
	0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x1a,
	0x2c, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61,
	0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x64,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f,
	0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2f, 0x66, 0x61, 0x71, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61,
	0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x70,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f,
	0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f,
	0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62,
	0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f,
	0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd2, 0x12, 0x0a, 0x0c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x08, 0x66, 0x61, 0x71, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46,
	0x61, 0x71, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x07, 0x66, 0x61, 0x71, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x53, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x0d, 0x74, 0x78, 0x6e, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x78, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x0b, 0x74, 0x78, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x50, 0x0a, 0x10, 0x74, 0x78, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x63,
	0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x54, 0x78, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x0e, 0x74, 0x78, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x60, 0x0a, 0x16, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x13,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x66, 0x0a, 0x18, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x62,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61,
	0x74, 0x61, 0x48, 0x00, 0x52, 0x15, 0x64, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12, 0x67, 0x0a, 0x19, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x78, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x15, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x78, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x4f, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x62, 0x0a, 0x16, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65,
	0x72, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x14, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x56, 0x0a, 0x12, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x10, 0x66, 0x65, 0x74, 0x63, 0x68, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x49, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68,
	0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0c,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x7d, 0x0a, 0x21,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1c, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0xb4, 0x01, 0x0a, 0x34,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x5f, 0x76, 0x6f, 0x75, 0x63,
	0x68, 0x65, 0x72, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x63, 0x78, 0x2e,
	0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x41, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x56, 0x6f, 0x75, 0x63, 0x68, 0x65,
	0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x2e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x41, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x56, 0x6f, 0x75, 0x63,
	0x68, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x4c, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x63, 0x0a, 0x17, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x78, 0x6e, 0x5f,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x54, 0x78, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x14, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x78, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x50, 0x0a, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f,
	0x74, 0x78, 0x6e, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x54, 0x78, 0x6e,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x54,
	0x78, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x7c, 0x0a, 0x20, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1c, 0x66, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0xa4, 0x01, 0x0a, 0x2e, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f,
	0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x00, 0x52, 0x29, 0x66, 0x65, 0x74, 0x63, 0x68, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6d, 0x0a, 0x1b,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x66, 0x6f, 0x72,
	0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x46, 0x6f, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x48, 0x00, 0x52, 0x17, 0x66, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x46,
	0x6f, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x62, 0x0a, 0x16, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x78,
	0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x14, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x7e, 0x0a, 0x20, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x78, 0x2e, 0x63,
	0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x1d, 0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x5c, 0x0a, 0x14, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x12, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x72, 0x0a,
	0x1c, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f,
	0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x19, 0x66, 0x65, 0x74, 0x63, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x96, 0x16, 0x0a, 0x13, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x4c, 0x0a, 0x0e, 0x66, 0x61, 0x71, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x78, 0x2e, 0x63,
	0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x46, 0x61, 0x71, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00,
	0x52, 0x0d, 0x66, 0x61, 0x71, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x65, 0x0a, 0x17, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2b, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52,
	0x15, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x59, 0x0a, 0x13, 0x74, 0x78, 0x6e, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f,
	0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x78, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x11,
	0x74, 0x78, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x62, 0x0a, 0x16, 0x74, 0x78, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x54, 0x78, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52,
	0x14, 0x74, 0x78, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x72, 0x0a, 0x1c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x78,
	0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x19,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x78, 0x0a, 0x1e, 0x64, 0x65, 0x62,
	0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x1b, 0x64, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x79, 0x0a, 0x1f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63,
	0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x78,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48,
	0x00, 0x52, 0x1b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x78, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x6e,
	0x0a, 0x1a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x48, 0x00, 0x52, 0x18, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x81,
	0x01, 0x0a, 0x21, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x78, 0x2e,
	0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x48, 0x00, 0x52, 0x1e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x73, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x58, 0x0a, 0x12, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x11, 0x64, 0x69, 0x73, 0x70, 0x75,
	0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x5c, 0x0a, 0x14,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x78, 0x2e,
	0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x12, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x8f, 0x01, 0x0a, 0x27, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63,
	0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x22, 0x66, 0x65, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0xba, 0x01, 0x0a,
	0x36, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x61, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x5f, 0x76, 0x6f, 0x75,
	0x63, 0x68, 0x65, 0x72, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x41, 0x6d, 0x61, 0x7a, 0x6f, 0x6e, 0x56, 0x6f, 0x75,
	0x63, 0x68, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x30, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x41, 0x6d, 0x61, 0x7a, 0x6f,
	0x6e, 0x56, 0x6f, 0x75, 0x63, 0x68, 0x65, 0x72, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x73, 0x0a, 0x1d, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x48, 0x00, 0x52, 0x19, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x6d,
	0x0a, 0x19, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x54, 0x78, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x16, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x78,
	0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x67, 0x0a,
	0x17, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x78, 0x6e, 0x73, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x54, 0x78, 0x6e, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00,
	0x52, 0x14, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x54, 0x78, 0x6e, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x8e, 0x01, 0x0a, 0x26, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x48, 0x00, 0x52, 0x22, 0x66, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0xb6, 0x01, 0x0a, 0x34, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74,
	0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52,
	0x2f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x7f, 0x0a, 0x21, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x78,
	0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x46, 0x6f,
	0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x48, 0x00, 0x52, 0x1d, 0x66, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x46,
	0x6f, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x78, 0x0a, 0x1c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52,
	0x1a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x94, 0x01, 0x0a, 0x26,
	0x70, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x63,
	0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x23, 0x70,
	0x72, 0x65, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x62, 0x0a, 0x16, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00,
	0x52, 0x14, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x84, 0x01, 0x0a, 0x22, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f,
	0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x48, 0x00, 0x52, 0x1f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x42, 0x13, 0x0a,
	0x11, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x22, 0x84, 0x01, 0x0a, 0x13, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5c, 0x0a, 0x14, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68,
	0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x48, 0x00, 0x52, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x76, 0x0a, 0x0c, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x14, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x48, 0x00, 0x52, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x62, 0x6f, 0x74, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x62, 0x6f, 0x74, 0x2f, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_chat_bot_workflow_messages_proto_rawDescOnce sync.Once
	file_api_cx_chat_bot_workflow_messages_proto_rawDescData = file_api_cx_chat_bot_workflow_messages_proto_rawDesc
)

func file_api_cx_chat_bot_workflow_messages_proto_rawDescGZIP() []byte {
	file_api_cx_chat_bot_workflow_messages_proto_rawDescOnce.Do(func() {
		file_api_cx_chat_bot_workflow_messages_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_chat_bot_workflow_messages_proto_rawDescData)
	})
	return file_api_cx_chat_bot_workflow_messages_proto_rawDescData
}

var file_api_cx_chat_bot_workflow_messages_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_cx_chat_bot_workflow_messages_proto_goTypes = []interface{}{
	(*WorkflowData)(nil),                                     // 0: cx.chat.bot.workflow.WorkflowData
	(*FetchDataParameters)(nil),                              // 1: cx.chat.bot.workflow.FetchDataParameters
	(*ExecuteActionParams)(nil),                              // 2: cx.chat.bot.workflow.ExecuteActionParams
	(*ActionResult)(nil),                                     // 3: cx.chat.bot.workflow.ActionResult
	(*FaqData)(nil),                                          // 4: cx.chat.bot.workflow.FaqData
	(*UserDetailsData)(nil),                                  // 5: cx.chat.bot.workflow.UserDetailsData
	(*TxnListData)(nil),                                      // 6: cx.chat.bot.workflow.TxnListData
	(*TxnDetailsData)(nil),                                   // 7: cx.chat.bot.workflow.TxnDetailsData
	(*CreditCardStateData)(nil),                              // 8: cx.chat.bot.workflow.CreditCardStateData
	(*DebitCardTrackingData)(nil),                            // 9: cx.chat.bot.workflow.DebitCardTrackingData
	(*CreditCardTxnListData)(nil),                            // 10: cx.chat.bot.workflow.CreditCardTxnListData
	(*EmploymentData)(nil),                                   // 11: cx.chat.bot.workflow.EmploymentData
	(*ScreenerAttemptsData)(nil),                             // 12: cx.chat.bot.workflow.ScreenerAttemptsData
	(*FetchDisputeData)(nil),                                 // 13: cx.chat.bot.workflow.FetchDisputeData
	(*LivenessData)(nil),                                     // 14: cx.chat.bot.workflow.LivenessData
	(*FetchRewardOffersForUserData)(nil),                     // 15: cx.chat.bot.workflow.FetchRewardOffersForUserData
	(*CheckSalaryProgramAmazonVoucherEligibilityData)(nil),   // 16: cx.chat.bot.workflow.CheckSalaryProgramAmazonVoucherEligibilityData
	(*ChargesListForActor)(nil),                              // 17: cx.chat.bot.workflow.ChargesListForActor
	(*DisplayTxnReasonData)(nil),                             // 18: cx.chat.bot.workflow.DisplayTxnReasonData
	(*FailedTxnsData)(nil),                                   // 19: cx.chat.bot.workflow.FailedTxnsData
	(*FetchRewardsEventDetailsData)(nil),                     // 20: cx.chat.bot.workflow.FetchRewardsEventDetailsData
	(*FetchSalaryProgramRegistrationDetailsData)(nil),        // 21: cx.chat.bot.workflow.FetchSalaryProgramRegistrationDetailsData
	(*FetchRewardForEventData)(nil),                          // 22: cx.chat.bot.workflow.FetchRewardForEventData
	(*ReleaseEvaluatorData)(nil),                             // 23: cx.chat.bot.workflow.ReleaseEvaluatorData
	(*PredefinedMessageTemplateData)(nil),                    // 24: cx.chat.bot.workflow.PredefinedMessageTemplateData
	(*BalanceRefreshData)(nil),                               // 25: cx.chat.bot.workflow.BalanceRefreshData
	(*FetchUserTransactionsData)(nil),                        // 26: cx.chat.bot.workflow.FetchUserTransactionsData
	(*FaqParameters)(nil),                                    // 27: cx.chat.bot.workflow.FaqParameters
	(*UserDetailsParameters)(nil),                            // 28: cx.chat.bot.workflow.UserDetailsParameters
	(*TxnListParameters)(nil),                                // 29: cx.chat.bot.workflow.TxnListParameters
	(*TxnDetailsParameters)(nil),                             // 30: cx.chat.bot.workflow.TxnDetailsParameters
	(*CreditCardStateParameters)(nil),                        // 31: cx.chat.bot.workflow.CreditCardStateParameters
	(*DebitCardTrackingParameters)(nil),                      // 32: cx.chat.bot.workflow.DebitCardTrackingParameters
	(*CreditCardTxnListParameters)(nil),                      // 33: cx.chat.bot.workflow.CreditCardTxnListParameters
	(*EmploymentDataParameters)(nil),                         // 34: cx.chat.bot.workflow.EmploymentDataParameters
	(*ScreenerAttemptsDataParameters)(nil),                   // 35: cx.chat.bot.workflow.ScreenerAttemptsDataParameters
	(*DisputeParameters)(nil),                                // 36: cx.chat.bot.workflow.DisputeParameters
	(*LivenessDataParams)(nil),                               // 37: cx.chat.bot.workflow.LivenessDataParams
	(*FetchRewardOffersForUserParameters)(nil),               // 38: cx.chat.bot.workflow.FetchRewardOffersForUserParameters
	(*CheckSalaryProgramAmazonVoucherEligibilityParams)(nil), // 39: cx.chat.bot.workflow.CheckSalaryProgramAmazonVoucherEligibilityParams
	(*ChargesListForActorParams)(nil),                        // 40: cx.chat.bot.workflow.ChargesListForActorParams
	(*DisplayTxnReasonParameters)(nil),                       // 41: cx.chat.bot.workflow.DisplayTxnReasonParameters
	(*FailedTxnsDataParameters)(nil),                         // 42: cx.chat.bot.workflow.FailedTxnsDataParameters
	(*FetchRewardsEventDetailsParameters)(nil),               // 43: cx.chat.bot.workflow.FetchRewardsEventDetailsParameters
	(*FetchSalaryProgramRegistrationDetailsParameters)(nil),  // 44: cx.chat.bot.workflow.FetchSalaryProgramRegistrationDetailsParameters
	(*FetchRewardForEventParameters)(nil),                    // 45: cx.chat.bot.workflow.FetchRewardForEventParameters
	(*ReleaseEvaluatorDataParameters)(nil),                   // 46: cx.chat.bot.workflow.ReleaseEvaluatorDataParameters
	(*PredefinedMessageTemplateDataParameters)(nil),          // 47: cx.chat.bot.workflow.PredefinedMessageTemplateDataParameters
	(*BalanceRefreshParams)(nil),                             // 48: cx.chat.bot.workflow.BalanceRefreshParams
	(*FetchUserTransactionsParameters)(nil),                  // 49: cx.chat.bot.workflow.FetchUserTransactionsParameters
	(*CreateTicketParams)(nil),                               // 50: cx.chat.bot.workflow.CreateTicketParams
	(*CreateTicketResult)(nil),                               // 51: cx.chat.bot.workflow.CreateTicketResult
}
var file_api_cx_chat_bot_workflow_messages_proto_depIdxs = []int32{
	4,  // 0: cx.chat.bot.workflow.WorkflowData.faq_data:type_name -> cx.chat.bot.workflow.FaqData
	5,  // 1: cx.chat.bot.workflow.WorkflowData.user_details_data:type_name -> cx.chat.bot.workflow.UserDetailsData
	6,  // 2: cx.chat.bot.workflow.WorkflowData.txn_list_data:type_name -> cx.chat.bot.workflow.TxnListData
	7,  // 3: cx.chat.bot.workflow.WorkflowData.txn_details_data:type_name -> cx.chat.bot.workflow.TxnDetailsData
	8,  // 4: cx.chat.bot.workflow.WorkflowData.credit_card_state_data:type_name -> cx.chat.bot.workflow.CreditCardStateData
	9,  // 5: cx.chat.bot.workflow.WorkflowData.debit_card_tracking_data:type_name -> cx.chat.bot.workflow.DebitCardTrackingData
	10, // 6: cx.chat.bot.workflow.WorkflowData.credit_card_txn_list_data:type_name -> cx.chat.bot.workflow.CreditCardTxnListData
	11, // 7: cx.chat.bot.workflow.WorkflowData.employment_data:type_name -> cx.chat.bot.workflow.EmploymentData
	12, // 8: cx.chat.bot.workflow.WorkflowData.screener_attempts_data:type_name -> cx.chat.bot.workflow.ScreenerAttemptsData
	13, // 9: cx.chat.bot.workflow.WorkflowData.fetch_dispute_data:type_name -> cx.chat.bot.workflow.FetchDisputeData
	14, // 10: cx.chat.bot.workflow.WorkflowData.liveness_data:type_name -> cx.chat.bot.workflow.LivenessData
	15, // 11: cx.chat.bot.workflow.WorkflowData.fetch_reward_offers_for_user_data:type_name -> cx.chat.bot.workflow.FetchRewardOffersForUserData
	16, // 12: cx.chat.bot.workflow.WorkflowData.check_salary_program_amazon_voucher_eligibility_data:type_name -> cx.chat.bot.workflow.CheckSalaryProgramAmazonVoucherEligibilityData
	17, // 13: cx.chat.bot.workflow.WorkflowData.charge_list:type_name -> cx.chat.bot.workflow.ChargesListForActor
	18, // 14: cx.chat.bot.workflow.WorkflowData.display_txn_reason_data:type_name -> cx.chat.bot.workflow.DisplayTxnReasonData
	19, // 15: cx.chat.bot.workflow.WorkflowData.failed_txns_data:type_name -> cx.chat.bot.workflow.FailedTxnsData
	20, // 16: cx.chat.bot.workflow.WorkflowData.fetch_rewards_event_details_data:type_name -> cx.chat.bot.workflow.FetchRewardsEventDetailsData
	21, // 17: cx.chat.bot.workflow.WorkflowData.fetch_salary_program_registration_details_data:type_name -> cx.chat.bot.workflow.FetchSalaryProgramRegistrationDetailsData
	22, // 18: cx.chat.bot.workflow.WorkflowData.fetch_reward_for_event_data:type_name -> cx.chat.bot.workflow.FetchRewardForEventData
	23, // 19: cx.chat.bot.workflow.WorkflowData.release_evaluator_data:type_name -> cx.chat.bot.workflow.ReleaseEvaluatorData
	24, // 20: cx.chat.bot.workflow.WorkflowData.predefined_message_template_data:type_name -> cx.chat.bot.workflow.PredefinedMessageTemplateData
	25, // 21: cx.chat.bot.workflow.WorkflowData.balance_refresh_data:type_name -> cx.chat.bot.workflow.BalanceRefreshData
	26, // 22: cx.chat.bot.workflow.WorkflowData.fetch_user_transactions_data:type_name -> cx.chat.bot.workflow.FetchUserTransactionsData
	27, // 23: cx.chat.bot.workflow.FetchDataParameters.faq_parameters:type_name -> cx.chat.bot.workflow.FaqParameters
	28, // 24: cx.chat.bot.workflow.FetchDataParameters.user_details_parameters:type_name -> cx.chat.bot.workflow.UserDetailsParameters
	29, // 25: cx.chat.bot.workflow.FetchDataParameters.txn_list_parameters:type_name -> cx.chat.bot.workflow.TxnListParameters
	30, // 26: cx.chat.bot.workflow.FetchDataParameters.txn_details_parameters:type_name -> cx.chat.bot.workflow.TxnDetailsParameters
	31, // 27: cx.chat.bot.workflow.FetchDataParameters.credit_card_state_parameters:type_name -> cx.chat.bot.workflow.CreditCardStateParameters
	32, // 28: cx.chat.bot.workflow.FetchDataParameters.debit_card_tracking_parameters:type_name -> cx.chat.bot.workflow.DebitCardTrackingParameters
	33, // 29: cx.chat.bot.workflow.FetchDataParameters.credit_card_txn_list_parameters:type_name -> cx.chat.bot.workflow.CreditCardTxnListParameters
	34, // 30: cx.chat.bot.workflow.FetchDataParameters.employment_data_parameters:type_name -> cx.chat.bot.workflow.EmploymentDataParameters
	35, // 31: cx.chat.bot.workflow.FetchDataParameters.screener_attempts_data_parameters:type_name -> cx.chat.bot.workflow.ScreenerAttemptsDataParameters
	36, // 32: cx.chat.bot.workflow.FetchDataParameters.dispute_parameters:type_name -> cx.chat.bot.workflow.DisputeParameters
	37, // 33: cx.chat.bot.workflow.FetchDataParameters.liveness_data_params:type_name -> cx.chat.bot.workflow.LivenessDataParams
	38, // 34: cx.chat.bot.workflow.FetchDataParameters.fetch_reward_offers_for_user_parameters:type_name -> cx.chat.bot.workflow.FetchRewardOffersForUserParameters
	39, // 35: cx.chat.bot.workflow.FetchDataParameters.check_salary_program_amazon_voucher_eligibility_params:type_name -> cx.chat.bot.workflow.CheckSalaryProgramAmazonVoucherEligibilityParams
	40, // 36: cx.chat.bot.workflow.FetchDataParameters.charges_list_for_actor_params:type_name -> cx.chat.bot.workflow.ChargesListForActorParams
	41, // 37: cx.chat.bot.workflow.FetchDataParameters.display_txn_reason_params:type_name -> cx.chat.bot.workflow.DisplayTxnReasonParameters
	42, // 38: cx.chat.bot.workflow.FetchDataParameters.failed_txns_data_params:type_name -> cx.chat.bot.workflow.FailedTxnsDataParameters
	43, // 39: cx.chat.bot.workflow.FetchDataParameters.fetch_rewards_event_details_parameters:type_name -> cx.chat.bot.workflow.FetchRewardsEventDetailsParameters
	44, // 40: cx.chat.bot.workflow.FetchDataParameters.fetch_salary_program_registration_details_parameters:type_name -> cx.chat.bot.workflow.FetchSalaryProgramRegistrationDetailsParameters
	45, // 41: cx.chat.bot.workflow.FetchDataParameters.fetch_reward_for_event_parameters:type_name -> cx.chat.bot.workflow.FetchRewardForEventParameters
	46, // 42: cx.chat.bot.workflow.FetchDataParameters.release_evaluator_parameters:type_name -> cx.chat.bot.workflow.ReleaseEvaluatorDataParameters
	47, // 43: cx.chat.bot.workflow.FetchDataParameters.predefined_message_template_parameters:type_name -> cx.chat.bot.workflow.PredefinedMessageTemplateDataParameters
	48, // 44: cx.chat.bot.workflow.FetchDataParameters.balance_refresh_params:type_name -> cx.chat.bot.workflow.BalanceRefreshParams
	49, // 45: cx.chat.bot.workflow.FetchDataParameters.fetch_user_transactions_parameters:type_name -> cx.chat.bot.workflow.FetchUserTransactionsParameters
	50, // 46: cx.chat.bot.workflow.ExecuteActionParams.create_ticket_params:type_name -> cx.chat.bot.workflow.CreateTicketParams
	51, // 47: cx.chat.bot.workflow.ActionResult.create_ticket_result:type_name -> cx.chat.bot.workflow.CreateTicketResult
	48, // [48:48] is the sub-list for method output_type
	48, // [48:48] is the sub-list for method input_type
	48, // [48:48] is the sub-list for extension type_name
	48, // [48:48] is the sub-list for extension extendee
	0,  // [0:48] is the sub-list for field type_name
}

func init() { file_api_cx_chat_bot_workflow_messages_proto_init() }
func file_api_cx_chat_bot_workflow_messages_proto_init() {
	if File_api_cx_chat_bot_workflow_messages_proto != nil {
		return
	}
	file_api_cx_chat_bot_workflow_create_ticket_proto_init()
	file_api_cx_chat_bot_workflow_credit_card_proto_init()
	file_api_cx_chat_bot_workflow_debit_card_proto_init()
	file_api_cx_chat_bot_workflow_dispute_proto_init()
	file_api_cx_chat_bot_workflow_employment_proto_init()
	file_api_cx_chat_bot_workflow_faq_proto_init()
	file_api_cx_chat_bot_workflow_liveness_proto_init()
	file_api_cx_chat_bot_workflow_predefined_message_template_proto_init()
	file_api_cx_chat_bot_workflow_release_evaluator_proto_init()
	file_api_cx_chat_bot_workflow_rewards_proto_init()
	file_api_cx_chat_bot_workflow_salary_program_proto_init()
	file_api_cx_chat_bot_workflow_savings_proto_init()
	file_api_cx_chat_bot_workflow_screener_proto_init()
	file_api_cx_chat_bot_workflow_transactions_proto_init()
	file_api_cx_chat_bot_workflow_user_details_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_chat_bot_workflow_messages_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkflowData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_chat_bot_workflow_messages_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDataParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_chat_bot_workflow_messages_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecuteActionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_chat_bot_workflow_messages_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_cx_chat_bot_workflow_messages_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*WorkflowData_FaqData)(nil),
		(*WorkflowData_UserDetailsData)(nil),
		(*WorkflowData_TxnListData)(nil),
		(*WorkflowData_TxnDetailsData)(nil),
		(*WorkflowData_CreditCardStateData)(nil),
		(*WorkflowData_DebitCardTrackingData)(nil),
		(*WorkflowData_CreditCardTxnListData)(nil),
		(*WorkflowData_EmploymentData)(nil),
		(*WorkflowData_ScreenerAttemptsData)(nil),
		(*WorkflowData_FetchDisputeData)(nil),
		(*WorkflowData_LivenessData)(nil),
		(*WorkflowData_FetchRewardOffersForUserData)(nil),
		(*WorkflowData_CheckSalaryProgramAmazonVoucherEligibilityData)(nil),
		(*WorkflowData_ChargeList)(nil),
		(*WorkflowData_DisplayTxnReasonData)(nil),
		(*WorkflowData_FailedTxnsData)(nil),
		(*WorkflowData_FetchRewardsEventDetailsData)(nil),
		(*WorkflowData_FetchSalaryProgramRegistrationDetailsData)(nil),
		(*WorkflowData_FetchRewardForEventData)(nil),
		(*WorkflowData_ReleaseEvaluatorData)(nil),
		(*WorkflowData_PredefinedMessageTemplateData)(nil),
		(*WorkflowData_BalanceRefreshData)(nil),
		(*WorkflowData_FetchUserTransactionsData)(nil),
	}
	file_api_cx_chat_bot_workflow_messages_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*FetchDataParameters_FaqParameters)(nil),
		(*FetchDataParameters_UserDetailsParameters)(nil),
		(*FetchDataParameters_TxnListParameters)(nil),
		(*FetchDataParameters_TxnDetailsParameters)(nil),
		(*FetchDataParameters_CreditCardStateParameters)(nil),
		(*FetchDataParameters_DebitCardTrackingParameters)(nil),
		(*FetchDataParameters_CreditCardTxnListParameters)(nil),
		(*FetchDataParameters_EmploymentDataParameters)(nil),
		(*FetchDataParameters_ScreenerAttemptsDataParameters)(nil),
		(*FetchDataParameters_DisputeParameters)(nil),
		(*FetchDataParameters_LivenessDataParams)(nil),
		(*FetchDataParameters_FetchRewardOffersForUserParameters)(nil),
		(*FetchDataParameters_CheckSalaryProgramAmazonVoucherEligibilityParams)(nil),
		(*FetchDataParameters_ChargesListForActorParams)(nil),
		(*FetchDataParameters_DisplayTxnReasonParams)(nil),
		(*FetchDataParameters_FailedTxnsDataParams)(nil),
		(*FetchDataParameters_FetchRewardsEventDetailsParameters)(nil),
		(*FetchDataParameters_FetchSalaryProgramRegistrationDetailsParameters)(nil),
		(*FetchDataParameters_FetchRewardForEventParameters)(nil),
		(*FetchDataParameters_ReleaseEvaluatorParameters)(nil),
		(*FetchDataParameters_PredefinedMessageTemplateParameters)(nil),
		(*FetchDataParameters_BalanceRefreshParams)(nil),
		(*FetchDataParameters_FetchUserTransactionsParameters)(nil),
	}
	file_api_cx_chat_bot_workflow_messages_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*ExecuteActionParams_CreateTicketParams)(nil),
	}
	file_api_cx_chat_bot_workflow_messages_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*ActionResult_CreateTicketResult)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_chat_bot_workflow_messages_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_chat_bot_workflow_messages_proto_goTypes,
		DependencyIndexes: file_api_cx_chat_bot_workflow_messages_proto_depIdxs,
		MessageInfos:      file_api_cx_chat_bot_workflow_messages_proto_msgTypes,
	}.Build()
	File_api_cx_chat_bot_workflow_messages_proto = out.File
	file_api_cx_chat_bot_workflow_messages_proto_rawDesc = nil
	file_api_cx_chat_bot_workflow_messages_proto_goTypes = nil
	file_api_cx_chat_bot_workflow_messages_proto_depIdxs = nil
}
