// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/header.proto

package cx

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	casbin "github.com/epifi/gamma/api/casbin"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	user "github.com/epifi/gamma/api/user"
	freshdesk "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// email of the agent loggedIn in sherlock
	AgentEmail string `protobuf:"bytes,1,opt,name=agent_email,json=agentEmail,proto3" json:"agent_email,omitempty"`
	// access token of the agent
	AccessToken string `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// ticket id for which the information is being accessed
	// should be sent only if ticket check is required for the method (this will be decided by method_options)
	TicketId int64 `protobuf:"varint,3,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	// user identifier
	// should be used only if user is calling from different number
	// agent has to manually enter user identifier on sherlock UI
	//
	// Types that are assignable to Identifier:
	//
	//	*Header_EmailId
	//	*Header_PhoneNumber
	Identifier isHeader_Identifier `protobuf_oneof:"identifier"`
	// ticket details
	// this is shouldn't be send as part of request
	// this object will populated by ticket validator interceptor to be used in all the methods
	// DISCLAIMER: the ticket populated in header is cached on cx side, it shouldn't be used in flows where latest ticket details are needed/critical.
	Ticket *freshdesk.Ticket `protobuf:"bytes,8,opt,name=ticket,proto3" json:"ticket,omitempty"`
	// this is shouldn't be send as part of request
	// this user object will populated by enricher interceptor to be used in all the methods
	User *user.User `protobuf:"bytes,9,opt,name=user,proto3" json:"user,omitempty"`
	// this is shouldn't be send as part of request
	// this user object will populated by enricher interceptor to be used in all the methods
	Actor *typesv2.Actor `protobuf:"bytes,10,opt,name=actor,proto3" json:"actor,omitempty"`
	// this shouldn't be send as part of request
	// this will be populated by interceptor after reading the value from method options
	InformationLevel InformationLevel `protobuf:"varint,11,opt,name=information_level,json=informationLevel,proto3,enum=cx.InformationLevel" json:"information_level,omitempty"`
	// access level of the user
	AccessLevel casbin.AccessLevel `protobuf:"varint,12,opt,name=access_level,json=accessLevel,proto3,enum=casbin.AccessLevel" json:"access_level,omitempty"`
	// monorail id of the issue for which data is being accessed
	// this should be passed in case of dev tool rpc's
	MonorailId int64 `protobuf:"varint,13,opt,name=monorail_id,json=monorailId,proto3" json:"monorail_id,omitempty"`
	// this shouldn't be passed as part of request
	// this will be used in customer auth flows to pass this info
	CustomerDetails *CustomerDetails `protobuf:"bytes,14,opt,name=customer_details,json=customerDetails,proto3" json:"customer_details,omitempty"`
	// this should be populated as part of request to inform backend which auth version has to be used
	// if not populated or V1 is specified than V1 single role implementation will be used
	// If V2 is specified than use multi role implementation
	AuthVersion AuthVersion `protobuf:"varint,15,opt,name=auth_version,json=authVersion,proto3,enum=cx.AuthVersion" json:"auth_version,omitempty"`
	// this should be populated as part of request if customer auth flow is called
	// anywhere outside regular ticket id flow
	// depending upon type specified, relevant actions are taken by the server
	CustomerAuthIdentifierType CustomerAuthIdentifierType `protobuf:"varint,16,opt,name=customer_auth_identifier_type,json=customerAuthIdentifierType,proto3,enum=cx.CustomerAuthIdentifierType" json:"customer_auth_identifier_type,omitempty"`
	// this field should be populated as part of request if CustomerAuthEntryIdentifierType is not UNSPECIFIED
	// this field will be used by server to maintain auth session information
	CustomerAuthIdentifierValue string `protobuf:"bytes,17,opt,name=customer_auth_identifier_value,json=customerAuthIdentifierValue,proto3" json:"customer_auth_identifier_value,omitempty"`
	// this field should be set to true from the client when requesting auth for insensitive data (CATEGORY FOUR AUTH)
	IsInsensitiveAuthRequested bool             `protobuf:"varint,18,opt,name=is_insensitive_auth_requested,json=isInsensitiveAuthRequested,proto3" json:"is_insensitive_auth_requested,omitempty"`
	DeploymentSource           DeploymentSource `protobuf:"varint,19,opt,name=deployment_source,json=deploymentSource,proto3,enum=cx.DeploymentSource" json:"deployment_source,omitempty"`
}

func (x *Header) Reset() {
	*x = Header{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_header_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_header_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_api_cx_header_proto_rawDescGZIP(), []int{0}
}

func (x *Header) GetAgentEmail() string {
	if x != nil {
		return x.AgentEmail
	}
	return ""
}

func (x *Header) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *Header) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

func (m *Header) GetIdentifier() isHeader_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *Header) GetEmailId() string {
	if x, ok := x.GetIdentifier().(*Header_EmailId); ok {
		return x.EmailId
	}
	return ""
}

func (x *Header) GetPhoneNumber() *common.PhoneNumber {
	if x, ok := x.GetIdentifier().(*Header_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return nil
}

func (x *Header) GetTicket() *freshdesk.Ticket {
	if x != nil {
		return x.Ticket
	}
	return nil
}

func (x *Header) GetUser() *user.User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Header) GetActor() *typesv2.Actor {
	if x != nil {
		return x.Actor
	}
	return nil
}

func (x *Header) GetInformationLevel() InformationLevel {
	if x != nil {
		return x.InformationLevel
	}
	return InformationLevel_INFORMATION_ACCESS_LEVEL_UNSPECIFIED
}

func (x *Header) GetAccessLevel() casbin.AccessLevel {
	if x != nil {
		return x.AccessLevel
	}
	return casbin.AccessLevel(0)
}

func (x *Header) GetMonorailId() int64 {
	if x != nil {
		return x.MonorailId
	}
	return 0
}

func (x *Header) GetCustomerDetails() *CustomerDetails {
	if x != nil {
		return x.CustomerDetails
	}
	return nil
}

func (x *Header) GetAuthVersion() AuthVersion {
	if x != nil {
		return x.AuthVersion
	}
	return AuthVersion_AUTH_VERSION_UNSPECIFIED
}

func (x *Header) GetCustomerAuthIdentifierType() CustomerAuthIdentifierType {
	if x != nil {
		return x.CustomerAuthIdentifierType
	}
	return CustomerAuthIdentifierType_CUSTOMER_AUTH_IDENTIFIER_TYPE_UNSPECIFIED
}

func (x *Header) GetCustomerAuthIdentifierValue() string {
	if x != nil {
		return x.CustomerAuthIdentifierValue
	}
	return ""
}

func (x *Header) GetIsInsensitiveAuthRequested() bool {
	if x != nil {
		return x.IsInsensitiveAuthRequested
	}
	return false
}

func (x *Header) GetDeploymentSource() DeploymentSource {
	if x != nil {
		return x.DeploymentSource
	}
	return DeploymentSource_DEPLOYMENT_SOURCE_UNSPECIFIED
}

type isHeader_Identifier interface {
	isHeader_Identifier()
}

type Header_EmailId struct {
	EmailId string `protobuf:"bytes,4,opt,name=email_id,json=emailId,proto3,oneof"`
}

type Header_PhoneNumber struct {
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

func (*Header_EmailId) isHeader_Identifier() {}

func (*Header_PhoneNumber) isHeader_Identifier() {}

type CustomerDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addresses map[string]*postaladdress.PostalAddress `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	User      *user.User                              `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *CustomerDetails) Reset() {
	*x = CustomerDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_header_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDetails) ProtoMessage() {}

func (x *CustomerDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_header_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDetails.ProtoReflect.Descriptor instead.
func (*CustomerDetails) Descriptor() ([]byte, []int) {
	return file_api_cx_header_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerDetails) GetAddresses() map[string]*postaladdress.PostalAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *CustomerDetails) GetUser() *user.User {
	if x != nil {
		return x.User
	}
	return nil
}

// We have incorporated token based pagination to make this RPC forward and backward compatible.
// This will allow us to implement pagination in backend without the need to make any change in client.This contains data
// relevant to the server in order to send the next page.
type PageToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// created timestamp of the last event on the page
	LastEventTimestamp *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=last_event_timestamp,json=lastEventTimestamp,proto3" json:"last_event_timestamp,omitempty"`
	// An offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp.
	// e.g. we can have 10 events starting with the timestamp start_timestamp. If offset is
	// set to 5 then first 5 records from the qualifying set are removed.
	EventOffset int32 `protobuf:"varint,2,opt,name=event_offset,json=eventOffset,proto3" json:"event_offset,omitempty"`
}

func (x *PageToken) Reset() {
	*x = PageToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_header_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageToken) ProtoMessage() {}

func (x *PageToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_header_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageToken.ProtoReflect.Descriptor instead.
func (*PageToken) Descriptor() ([]byte, []int) {
	return file_api_cx_header_proto_rawDescGZIP(), []int{2}
}

func (x *PageToken) GetLastEventTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastEventTimestamp
	}
	return nil
}

func (x *PageToken) GetEventOffset() int32 {
	if x != nil {
		return x.EventOffset
	}
	return 0
}

// page context to set offset, and limit in the request
// 1st request will have *limit* flag to specify number of elements from backend
// backend will send `PageContext` -- setting before_token and after_token
// next paginated request will send the same PageContext which will have 2 tokens and 2 flags
// to identify `next` and `before` token to paginate forward and backward
// read more on this - "https://hackernoon.com/guys-were-doing-pagination-wrong-f6c18a91b232"
type PageContextResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token to paginate backward in the list
	BeforeToken string `protobuf:"bytes,1,opt,name=before_token,json=beforeToken,proto3" json:"before_token,omitempty"`
	// if items are present before before_token
	HasBefore bool `protobuf:"varint,2,opt,name=has_before,json=hasBefore,proto3" json:"has_before,omitempty"`
	// token to paginate forward in the list
	AfterToken string `protobuf:"bytes,3,opt,name=after_token,json=afterToken,proto3" json:"after_token,omitempty"`
	// if items are present after after_token
	HasAfter bool `protobuf:"varint,4,opt,name=has_after,json=hasAfter,proto3" json:"has_after,omitempty"`
}

func (x *PageContextResponse) Reset() {
	*x = PageContextResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_header_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageContextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageContextResponse) ProtoMessage() {}

func (x *PageContextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_header_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageContextResponse.ProtoReflect.Descriptor instead.
func (*PageContextResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_header_proto_rawDescGZIP(), []int{3}
}

func (x *PageContextResponse) GetBeforeToken() string {
	if x != nil {
		return x.BeforeToken
	}
	return ""
}

func (x *PageContextResponse) GetHasBefore() bool {
	if x != nil {
		return x.HasBefore
	}
	return false
}

func (x *PageContextResponse) GetAfterToken() string {
	if x != nil {
		return x.AfterToken
	}
	return ""
}

func (x *PageContextResponse) GetHasAfter() bool {
	if x != nil {
		return x.HasAfter
	}
	return false
}

type PageContextRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Token:
	//
	//	*PageContextRequest_BeforeToken
	//	*PageContextRequest_AfterToken
	Token isPageContextRequest_Token `protobuf_oneof:"token"`
	// page size
	PageSize uint32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *PageContextRequest) Reset() {
	*x = PageContextRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_header_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageContextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageContextRequest) ProtoMessage() {}

func (x *PageContextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_header_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageContextRequest.ProtoReflect.Descriptor instead.
func (*PageContextRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_header_proto_rawDescGZIP(), []int{4}
}

func (m *PageContextRequest) GetToken() isPageContextRequest_Token {
	if m != nil {
		return m.Token
	}
	return nil
}

func (x *PageContextRequest) GetBeforeToken() string {
	if x, ok := x.GetToken().(*PageContextRequest_BeforeToken); ok {
		return x.BeforeToken
	}
	return ""
}

func (x *PageContextRequest) GetAfterToken() string {
	if x, ok := x.GetToken().(*PageContextRequest_AfterToken); ok {
		return x.AfterToken
	}
	return ""
}

func (x *PageContextRequest) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type isPageContextRequest_Token interface {
	isPageContextRequest_Token()
}

type PageContextRequest_BeforeToken struct {
	BeforeToken string `protobuf:"bytes,1,opt,name=before_token,json=beforeToken,proto3,oneof"`
}

type PageContextRequest_AfterToken struct {
	AfterToken string `protobuf:"bytes,2,opt,name=after_token,json=afterToken,proto3,oneof"`
}

func (*PageContextRequest_BeforeToken) isPageContextRequest_Token() {}

func (*PageContextRequest_AfterToken) isPageContextRequest_Token() {}

var File_api_cx_header_proto protoreflect.FileDescriptor

var file_api_cx_header_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x63, 0x78, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x61, 0x73, 0x62, 0x69, 0x6e, 0x2f, 0x63, 0x61, 0x73, 0x62, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63,
	0x78, 0x2f, 0x66, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x2f, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90, 0x07, 0x0a, 0x06,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x06, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x78, 0x2e,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65, 0x73, 0x6b, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1e, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x05, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x05, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x41, 0x0a, 0x11, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x63, 0x78, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x52, 0x10, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x36, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x61,
	0x73, 0x62, 0x69, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x6f, 0x6e, 0x6f, 0x72, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x6d, 0x6f, 0x6e, 0x6f, 0x72, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x3e,
	0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x78, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x32,
	0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x78, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x61, 0x0a, 0x1d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x63, 0x78, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x1e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x41, 0x0a, 0x1d, 0x69, 0x73,
	0x5f, 0x69, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x75, 0x74,
	0x68, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x1a, 0x69, 0x73, 0x49, 0x6e, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65,
	0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x41, 0x0a,
	0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x78, 0x2e, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x10,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xcd,
	0x01, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x40, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x1a, 0x58, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7c,
	0x0a, 0x09, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x4c, 0x0a, 0x14, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x12, 0x6c, 0x61, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x95, 0x01, 0x0a,
	0x13, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x61, 0x73, 0x5f, 0x62,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x68, 0x61, 0x73,
	0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x66, 0x74,
	0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x5f, 0x61,
	0x66, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68, 0x61, 0x73, 0x41,
	0x66, 0x74, 0x65, 0x72, 0x22, 0x82, 0x01, 0x0a, 0x12, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0c, 0x62,
	0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x0b, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x21, 0x0a, 0x0b, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x66, 0x74, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x42, 0x07, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x3e, 0x0a, 0x1d, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x5a, 0x1d, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_cx_header_proto_rawDescOnce sync.Once
	file_api_cx_header_proto_rawDescData = file_api_cx_header_proto_rawDesc
)

func file_api_cx_header_proto_rawDescGZIP() []byte {
	file_api_cx_header_proto_rawDescOnce.Do(func() {
		file_api_cx_header_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_header_proto_rawDescData)
	})
	return file_api_cx_header_proto_rawDescData
}

var file_api_cx_header_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_cx_header_proto_goTypes = []interface{}{
	(*Header)(nil),                      // 0: cx.Header
	(*CustomerDetails)(nil),             // 1: cx.CustomerDetails
	(*PageToken)(nil),                   // 2: cx.PageToken
	(*PageContextResponse)(nil),         // 3: cx.PageContextResponse
	(*PageContextRequest)(nil),          // 4: cx.PageContextRequest
	nil,                                 // 5: cx.CustomerDetails.AddressesEntry
	(*common.PhoneNumber)(nil),          // 6: api.typesv2.common.PhoneNumber
	(*freshdesk.Ticket)(nil),            // 7: cx.freshdesk.Ticket
	(*user.User)(nil),                   // 8: user.User
	(*typesv2.Actor)(nil),               // 9: api.typesv2.Actor
	(InformationLevel)(0),               // 10: cx.InformationLevel
	(casbin.AccessLevel)(0),             // 11: casbin.AccessLevel
	(AuthVersion)(0),                    // 12: cx.AuthVersion
	(CustomerAuthIdentifierType)(0),     // 13: cx.CustomerAuthIdentifierType
	(DeploymentSource)(0),               // 14: cx.DeploymentSource
	(*timestamppb.Timestamp)(nil),       // 15: google.protobuf.Timestamp
	(*postaladdress.PostalAddress)(nil), // 16: google.type.PostalAddress
}
var file_api_cx_header_proto_depIdxs = []int32{
	6,  // 0: cx.Header.phone_number:type_name -> api.typesv2.common.PhoneNumber
	7,  // 1: cx.Header.ticket:type_name -> cx.freshdesk.Ticket
	8,  // 2: cx.Header.user:type_name -> user.User
	9,  // 3: cx.Header.actor:type_name -> api.typesv2.Actor
	10, // 4: cx.Header.information_level:type_name -> cx.InformationLevel
	11, // 5: cx.Header.access_level:type_name -> casbin.AccessLevel
	1,  // 6: cx.Header.customer_details:type_name -> cx.CustomerDetails
	12, // 7: cx.Header.auth_version:type_name -> cx.AuthVersion
	13, // 8: cx.Header.customer_auth_identifier_type:type_name -> cx.CustomerAuthIdentifierType
	14, // 9: cx.Header.deployment_source:type_name -> cx.DeploymentSource
	5,  // 10: cx.CustomerDetails.addresses:type_name -> cx.CustomerDetails.AddressesEntry
	8,  // 11: cx.CustomerDetails.user:type_name -> user.User
	15, // 12: cx.PageToken.last_event_timestamp:type_name -> google.protobuf.Timestamp
	16, // 13: cx.CustomerDetails.AddressesEntry.value:type_name -> google.type.PostalAddress
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_cx_header_proto_init() }
func file_api_cx_header_proto_init() {
	if File_api_cx_header_proto != nil {
		return
	}
	file_api_cx_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_header_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Header); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_header_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_header_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_header_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageContextResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_header_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageContextRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_cx_header_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Header_EmailId)(nil),
		(*Header_PhoneNumber)(nil),
	}
	file_api_cx_header_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*PageContextRequest_BeforeToken)(nil),
		(*PageContextRequest_AfterToken)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_header_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_header_proto_goTypes,
		DependencyIndexes: file_api_cx_header_proto_depIdxs,
		MessageInfos:      file_api_cx_header_proto_msgTypes,
	}.Build()
	File_api_cx_header_proto = out.File
	file_api_cx_header_proto_rawDesc = nil
	file_api_cx_header_proto_goTypes = nil
	file_api_cx_header_proto_depIdxs = nil
}
