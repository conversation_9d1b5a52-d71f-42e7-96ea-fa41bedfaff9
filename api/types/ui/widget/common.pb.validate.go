// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/types/ui/widget/common.proto

package widget

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ImageTitleSubtitleElement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ImageTitleSubtitleElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageTitleSubtitleElement with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImageTitleSubtitleElementMultiError, or nil if none found.
func (m *ImageTitleSubtitleElement) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageTitleSubtitleElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIconImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageTitleSubtitleElementValidationError{
					field:  "IconImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageTitleSubtitleElementValidationError{
					field:  "IconImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIconImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageTitleSubtitleElementValidationError{
				field:  "IconImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitleText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageTitleSubtitleElementValidationError{
					field:  "TitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageTitleSubtitleElementValidationError{
					field:  "TitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageTitleSubtitleElementValidationError{
				field:  "TitleText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitleText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageTitleSubtitleElementValidationError{
					field:  "SubtitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageTitleSubtitleElementValidationError{
					field:  "SubtitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitleText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageTitleSubtitleElementValidationError{
				field:  "SubtitleText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BackgroundColor

	if len(errors) > 0 {
		return ImageTitleSubtitleElementMultiError(errors)
	}

	return nil
}

// ImageTitleSubtitleElementMultiError is an error wrapping multiple validation
// errors returned by ImageTitleSubtitleElement.ValidateAll() if the
// designated constraints aren't met.
type ImageTitleSubtitleElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageTitleSubtitleElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageTitleSubtitleElementMultiError) AllErrors() []error { return m }

// ImageTitleSubtitleElementValidationError is the validation error returned by
// ImageTitleSubtitleElement.Validate if the designated constraints aren't met.
type ImageTitleSubtitleElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageTitleSubtitleElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageTitleSubtitleElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageTitleSubtitleElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageTitleSubtitleElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageTitleSubtitleElementValidationError) ErrorName() string {
	return "ImageTitleSubtitleElementValidationError"
}

// Error satisfies the builtin error interface
func (e ImageTitleSubtitleElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageTitleSubtitleElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageTitleSubtitleElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageTitleSubtitleElementValidationError{}

// Validate checks the field values on CheckboxItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckboxItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckboxItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CheckboxItemMultiError, or
// nil if none found.
func (m *CheckboxItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckboxItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetDisplayText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckboxItemValidationError{
					field:  "DisplayText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckboxItemValidationError{
					field:  "DisplayText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckboxItemValidationError{
				field:  "DisplayText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsChecked

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckboxItemValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckboxItemValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckboxItemValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckboxItemMultiError(errors)
	}

	return nil
}

// CheckboxItemMultiError is an error wrapping multiple validation errors
// returned by CheckboxItem.ValidateAll() if the designated constraints aren't met.
type CheckboxItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckboxItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckboxItemMultiError) AllErrors() []error { return m }

// CheckboxItemValidationError is the validation error returned by
// CheckboxItem.Validate if the designated constraints aren't met.
type CheckboxItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckboxItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckboxItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckboxItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckboxItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckboxItemValidationError) ErrorName() string { return "CheckboxItemValidationError" }

// Error satisfies the builtin error interface
func (e CheckboxItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckboxItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckboxItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckboxItemValidationError{}

// Validate checks the field values on VisualElementTitleSubtitleElement with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VisualElementTitleSubtitleElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VisualElementTitleSubtitleElement
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VisualElementTitleSubtitleElementMultiError, or nil if none found.
func (m *VisualElementTitleSubtitleElement) ValidateAll() error {
	return m.validate(true)
}

func (m *VisualElementTitleSubtitleElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VisualElementTitleSubtitleElementValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VisualElementTitleSubtitleElementValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VisualElementTitleSubtitleElementValidationError{
				field:  "VisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitleText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VisualElementTitleSubtitleElementValidationError{
					field:  "TitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VisualElementTitleSubtitleElementValidationError{
					field:  "TitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VisualElementTitleSubtitleElementValidationError{
				field:  "TitleText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitleText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VisualElementTitleSubtitleElementValidationError{
					field:  "SubtitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VisualElementTitleSubtitleElementValidationError{
					field:  "SubtitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitleText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VisualElementTitleSubtitleElementValidationError{
				field:  "SubtitleText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BackgroundColor

	if len(errors) > 0 {
		return VisualElementTitleSubtitleElementMultiError(errors)
	}

	return nil
}

// VisualElementTitleSubtitleElementMultiError is an error wrapping multiple
// validation errors returned by
// VisualElementTitleSubtitleElement.ValidateAll() if the designated
// constraints aren't met.
type VisualElementTitleSubtitleElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VisualElementTitleSubtitleElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VisualElementTitleSubtitleElementMultiError) AllErrors() []error { return m }

// VisualElementTitleSubtitleElementValidationError is the validation error
// returned by VisualElementTitleSubtitleElement.Validate if the designated
// constraints aren't met.
type VisualElementTitleSubtitleElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VisualElementTitleSubtitleElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VisualElementTitleSubtitleElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VisualElementTitleSubtitleElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VisualElementTitleSubtitleElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VisualElementTitleSubtitleElementValidationError) ErrorName() string {
	return "VisualElementTitleSubtitleElementValidationError"
}

// Error satisfies the builtin error interface
func (e VisualElementTitleSubtitleElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVisualElementTitleSubtitleElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VisualElementTitleSubtitleElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VisualElementTitleSubtitleElementValidationError{}
