syntax = "proto3";

package rewards.pinot;

import "api/order/payment/payment_protocol.proto";
import "api/order/workflow.proto";
import "api/rewards/collected_data_type.proto";
import "api/rewards/reward.proto";
import "api/rewards/reward_offer_type.proto";
import "api/tiering/external/external.proto";
import "api/categorizer/enums.proto";

option go_package = "github.com/epifi/gamma/api/rewards/pinot";
option java_package = "com.github.epifi.gamma.api.rewards.pinot";

// TODO : To be used later
// Projection proto for pinot reward_projections table
message Projection {
  // unique identifier for the projection
  string id = 1 [json_name = "id"];
  // unique identifier for a user
  string actor_id = 2 [json_name="actor_id"];
  // identifier of the reward offer for which the projection is generated
  string offer_id = 3 [json_name = "offer_id"];
  // identifier for the merchant of transaction for which the projection is generated
  string merchant_id = 4 [json_name = "merchant_id"];
  // denotes the merchant name for event against which the projection was generated
  // for e.g. Amazon,Swiggy etc
  string merchant_name = 5 [json_name = "merchant_name"];
  // the particular account for the actor, if the user has multiple accounts,
  // for e.g. savings,cc account
  string account_id = 6 [json_name = "account_id"];
  // identifier for the reward that is generated corresponding to this projection
  string reward_id = 7 [json_name = "reward_id"];
  // ontology ids of the category assigned to the transaction from perspective of actor
  // matched with a set of ontology ids in OR fashion to generate reward
  repeated string ds_ontology_ids = 8 [json_name = "ds_ontology_ids"];
  // l0 ontologies of the transaction from ds categorizer
  repeated categorizer.L0 ds_l0_ontologies = 9 [json_name = "ds_l0_ontologies"];
  // reward offer type
  RewardOfferType offer_type = 10 [json_name = "offer_type"];
  // denotes the payment protocol used if the projection is generated on a transaction,
  // for e.g. UPI,Card etc
  order.payment.PaymentProtocol payment_protocol = 11 [json_name = "payment_protocol"];
  // denotes the user tier at the time of reward generation
  tiering.external.Tier account_tier = 12 [json_name = "account_tier"];
  // denotes the reward type of projection option 1
  RewardType option1_projection_reward_type = 13 [json_name = "option1_projection_reward_type"];
  // denotes the reward type of projection option 2
  RewardType option2_projection_reward_type = 14 [json_name = "option2_projection_reward_type"];
  // denotes the reward units to be generated for projection option 1
  float option1_projection_reward_units = 15 [json_name = "option1_projection_reward_units"];
  // denotes the reward units to be generated for projection option 2
  float option2_projection_reward_units = 16 [json_name = "option2_projection_reward_units"];
  // denotes the actual reward units to be generated against projections option 1 (contributing to the final reward after applying different caps)
  float option1_contribution_reward_units = 17 [json_name = "option1_contribution_reward_units"];
  // denotes the actual reward units to be generated against projections option 2 (contributing to the final reward after applying different caps)
  float option2_contribution_reward_units = 18 [json_name = "option2_contribution_reward_units"];
  // denotes the type of event for which the projection is being generated
  CollectedDataType action_type = 19 [json_name = "action_type"];
  // workflow of order event for which the reward was generated
  order.OrderWorkflow order_workflow = 20 [json_name = "order_workflow"];
  // denotes the time at which the event action for which projection is getting generated was performed by the user
  int64 action_time = 21 [json_name = "action_time"];
  // denotes the time when the projection was created
  int64 created_at = 22 [json_name = "created_at"];
  // denotes the time when any information in the projection was last updated,
  // for e.g. status,substatus etc
  int64 updated_at = 23 [json_name = "updated_at"];
  // denotes whether the projection has to be deleted while an upsert (soft-deleted)
  bool delete_entry = 24 [json_name = "delete_entry"];
}
