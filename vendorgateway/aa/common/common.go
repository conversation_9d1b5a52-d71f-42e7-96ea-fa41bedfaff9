package common

import (
	"net/http"
	"time"

	venAaPb "github.com/epifi/gamma/api/vendors/aa"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/vendorgateway/aa"
	"github.com/epifi/gamma/api/vendors/aa/onemoney"
)

// Interface to store marshalled request and use it when signing the request and setting in header
// Kind of cache so that request is not marshalled twice while setting in header and when sending http call
// All AA requests will implement this interface so that common functions like adding api key to header
// adding signature to header from payload can be done at one place
type RequestMarshal interface {
	GetMarshalled() []byte             // getter
	SetMarshalled([]byte)              // setter
	CreateMarshalled() ([]byte, error) // actual marshalled payload creation
}

// Method to return marshalled payload needed for the request
// This will return the payload from the object if already set
// If anyone calls Add for header and Marshal for request interchangeably this method ensures that only time we marhsal
// request and also protects from not adding signature to header accidentally
func GetMarshalledPayload(req interface{}) ([]byte, error) {
	rm, ok := req.(RequestMarshal)
	if ok {
		if len(rm.GetMarshalled()) == 0 {
			by, err := rm.CreateMarshalled()
			if err != nil {
				return nil, err
			}
			rm.SetMarshalled(by)
			return by, nil
		}
		return rm.GetMarshalled(), nil
	}
	return nil, errors.New("request does not implement RequestMarshal interface")
}

// Common attributes needed in all AA API request and responses
// ApiKey : Obtained from token server/cache in UAT and PROD and config in other env, to be set as client_api_key
// vendorapi.ISigner : embbeded signer to sign payload and add detached signature to request header and also verify
// received detached jws signature in http response header (UAT and PROD)
// MarshalledBody : Store marshalled bytes for the request, needed for generating and verifying sign
// BaseUrl : base url of all AA requests obtained from CR or config based on env
type CommonAttribute struct {
	ApiKey string
	vendorapi.ISigner
	MarshalledBody []byte
	BaseUrl        string
}

// Getter
func (c *CommonAttribute) GetMarshalled() []byte {
	return c.MarshalledBody
}

// Setter
func (c *CommonAttribute) SetMarshalled(b []byte) {
	c.MarshalledBody = b
}

// Common method to add headers in all AA API calls
// x-jws-signature : detached signature of the body/endpointuri using epifi private key
// client_api_key : auth token obtained from token server/config
func (c *CommonAttribute) AddHeaders(req *http.Request, rm RequestMarshal) *http.Request {
	// req.Header.Add converts header key name to canonical format, we cannot use that because vendor reads header key
	// as client_api_key while we will end up sending as Client_api_key
	req.Header["client_api_key"] = []string{c.ApiKey}
	if c.IsSignatureNeeded() {
		by, err := GetMarshalledPayload(rm)
		if err != nil {
			logger.ErrorNoCtx("cannot get marshalled body", zap.Error(err))
			return nil
		}
		signature, err := c.GetSigner().GenerateDetachedJwsSignature(by)
		if err != nil {
			logger.ErrorNoCtx("cannot generate jws signature from body", zap.Error(err))
			return nil
		}
		req.Header["x-jws-signature"] = []string{signature}
	}
	return req
}

func GetTimestampPbFromString(timeString string, layout string) (*timestamppb.Timestamp, error) {
	parsedTime, timeParseErr := time.ParseInLocation(layout, timeString, time.UTC)
	if timeParseErr != nil {
		// If we encounter error then we try to parse using iso 8601 method
		pt, timeParseErr := datetime.ParseIso8601DateTimeString(timeString)
		if timeParseErr != nil {
			return nil, timeParseErr
		}
		return pt, nil
	}
	return timestamppb.New(parsedTime), nil
}

func toAAFIList(fiList []*onemoney.FI) ([]*aa.FI, error) {
	var aaFIList []*aa.FI
	for _, fi := range fiList {
		var aaDataList []*aa.Data
		for _, data := range fi.GetData() {
			if data.GetLinkRefNumber() == "" || data.GetMaskedAccNumber() == "" || data.GetEncryptedFi() == "" {
				return nil, errors.New("empty fields in data in vendor response")
			}
			aaData := &aa.Data{
				LinkRefNumber:   data.GetLinkRefNumber(),
				MaskedAccNumber: data.GetMaskedAccNumber(),
				EncryptedFi:     data.GetEncryptedFi(),
			}
			aaDataList = append(aaDataList, aaData)
		}
		expiry, timestampErr := GetTimestampPbFromString(fi.GetKeyMaterial().GetDhPublicKey().GetExpiry(), TimestampFormat)
		if timestampErr != nil {
			return nil, errors.Wrap(timestampErr, "error parsing key timestamp from vendor")
		}
		if expiry.AsTime().Before(time.Now().UTC()) {
			return nil, errors.New("expired key from fetch data response")
		}
		aaFi := &aa.FI{
			FipId: fi.GetFipId(),
			Data:  aaDataList,
			KeyMaterial: &aa.KeyMaterial{
				CryptoAlgo: toCrypAlgoEnum(fi.GetKeyMaterial().GetCryptoAlgo()),
				Curve:      toCrypCurveEnum(fi.GetKeyMaterial().GetCurve()),
				KeyExpiry:  expiry,
				PublicKey:  fi.GetKeyMaterial().GetDhPublicKey().GetKeyValue(),
				Nonce:      fi.GetKeyMaterial().GetNonce(),
			},
		}
		if aaFi.GetFipId() == "" || aaFi.GetKeyMaterial().GetNonce() == "" || aaFi.GetKeyMaterial().GetPublicKey() == "" ||
			aaFi.GetKeyMaterial().GetCryptoAlgo() == aa.CryptoAlgo_CRYPTO_ALGO_UNSPECIFIED ||
			aaFi.GetKeyMaterial().GetCurve() == aa.CryptoCurve_CRYPTO_CURVE_UNSPECIFIED {
			return nil, errors.New("invalid key material received from vendor")
		}
		aaFIList = append(aaFIList, aaFi)
	}
	return aaFIList, nil
}

func toOMDataFilter(filter []*aa.DataFilter) []*onemoney.DataFilter {
	var dfList []*onemoney.DataFilter
	for _, fil := range filter {
		df := &onemoney.DataFilter{
			Operator: fil.GetOperator(),
			Value:    fil.GetValue(),
		}
		switch fil.GetType() {
		case aa.DataFilterType_DATA_FILTER_TYPE_TRANSACTION_AMT:
			df.Type = TransactionAmt
		case aa.DataFilterType_DATA_FILTER_TYPE_TRANSACTION_TYPE:
			df.Type = TransactionType
		default:
		}
		dfList = append(dfList, df)
	}
	return dfList
}

func toAADataFilter(filter []*onemoney.DataFilter) []*aa.DataFilter {
	var dfList []*aa.DataFilter
	for _, fil := range filter {
		df := &aa.DataFilter{
			Operator: fil.GetOperator(),
			Value:    fil.GetValue(),
		}
		switch fil.GetType() {
		case TransactionAmt:
			df.Type = aa.DataFilterType_DATA_FILTER_TYPE_TRANSACTION_AMT
		case TransactionType:
			df.Type = aa.DataFilterType_DATA_FILTER_TYPE_TRANSACTION_TYPE
		default:
			df.Type = aa.DataFilterType_DATA_FILTER_TYPE_UNSPECIFIED
		}
		dfList = append(dfList, df)
	}
	return dfList
}

func toOMFrequency(frequency *aa.Frequency) *onemoney.Frequency {
	frequencyOm := &onemoney.Frequency{
		Value: frequency.GetValue(),
	}
	switch frequency.GetUnit() {
	case aa.FrequencyUnit_FREQUENCY_UNIT_HOUR:
		frequencyOm.Unit = Hour
	case aa.FrequencyUnit_FREQUENCY_UNIT_DAY:
		frequencyOm.Unit = Day
	case aa.FrequencyUnit_FREQUENCY_UNIT_MONTH:
		frequencyOm.Unit = Month
	case aa.FrequencyUnit_FREQUENCY_UNIT_YEAR:
		frequencyOm.Unit = Year
	default:
	}
	return frequencyOm
}

func toAAFrequency(frequency *onemoney.Frequency) *aa.Frequency {
	frequencyAA := &aa.Frequency{
		Value: frequency.GetValue(),
	}
	switch frequency.GetUnit() {
	case Hour:
		frequencyAA.Unit = aa.FrequencyUnit_FREQUENCY_UNIT_HOUR
	case Day:
		frequencyAA.Unit = aa.FrequencyUnit_FREQUENCY_UNIT_DAY
	case Month:
		frequencyAA.Unit = aa.FrequencyUnit_FREQUENCY_UNIT_MONTH
	case Year:
		frequencyAA.Unit = aa.FrequencyUnit_FREQUENCY_UNIT_YEAR
	default:
		frequencyAA.Unit = aa.FrequencyUnit_FREQUENCY_UNIT_UNSPECIFIED
	}
	return frequencyAA
}

func toOmDataLife(dataLife *aa.DataLife) *onemoney.DataLife {
	dataLifeOm := &onemoney.DataLife{
		Value: dataLife.GetValue(),
	}
	switch dataLife.GetUnit() {
	case aa.DataLifeUnit_DATA_LIFE_DAY:
		dataLifeOm.Unit = Day
	case aa.DataLifeUnit_DATA_LIFE_MONTH:
		dataLifeOm.Unit = Month
	case aa.DataLifeUnit_DATA_LIFE_YEAR:
		dataLifeOm.Unit = Year
	case aa.DataLifeUnit_DATA_LIFE_INF:
		dataLifeOm.Unit = Inf
		dataLifeOm.Value = 0
	default:
	}
	return dataLifeOm
}

func toAADataLife(dataLife *onemoney.DataLife) *aa.DataLife {
	dataLifeAA := &aa.DataLife{
		Value: dataLife.GetValue(),
	}
	switch dataLife.GetUnit() {
	case Day:
		dataLifeAA.Unit = aa.DataLifeUnit_DATA_LIFE_DAY
	case Month:
		dataLifeAA.Unit = aa.DataLifeUnit_DATA_LIFE_MONTH
	case Year:
		dataLifeAA.Unit = aa.DataLifeUnit_DATA_LIFE_YEAR
	case Inf:
		dataLifeAA.Unit = aa.DataLifeUnit_DATA_LIFE_INF
		dataLifeAA.Value = 0
	default:
		dataLifeAA.Unit = aa.DataLifeUnit_DATA_LIFE_UNSPECIFIED
	}
	return dataLifeAA
}

func toFiTypesString(fiTypes []aa.FIType) []string {
	var fiTypeList []string
	for _, fiType := range fiTypes {
		switch fiType {
		case aa.FIType_FI_TYPE_DEPOSIT:
			fiTypeList = append(fiTypeList, Deposit)
		case aa.FIType_FI_TYPE_RECURRING_DEPOSIT:
			fiTypeList = append(fiTypeList, RecurringDeposit)
		case aa.FIType_FI_TYPE_TERM_DEPOSIT:
			fiTypeList = append(fiTypeList, TermDeposit2)
		case aa.FIType_FI_TYPE_MUTUAL_FUNDS:
			fiTypeList = append(fiTypeList, MutualFunds)
		case aa.FIType_FI_TYPE_EQUITIES:
			fiTypeList = append(fiTypeList, Equities)
		case aa.FIType_FI_TYPE_NPS:
			fiTypeList = append(fiTypeList, Nps)
		case aa.FIType_FI_TYPE_ETF:
			fiTypeList = append(fiTypeList, Etf)
		case aa.FIType_FI_TYPE_REIT:
			fiTypeList = append(fiTypeList, Reit)
		case aa.FIType_FI_TYPE_INVIT:
			fiTypeList = append(fiTypeList, Invit)
		default:
		}
	}
	return fiTypeList
}

func toFiTypesEnum(fiTypes []string) []aa.FIType {
	var fiTypeList []aa.FIType
	for _, fiType := range fiTypes {
		switch fiType {
		case Deposit:
			fiTypeList = append(fiTypeList, aa.FIType_FI_TYPE_DEPOSIT)
		case RecurringDeposit:
			fiTypeList = append(fiTypeList, aa.FIType_FI_TYPE_RECURRING_DEPOSIT)
		case TermDeposit1, TermDeposit2:
			fiTypeList = append(fiTypeList, aa.FIType_FI_TYPE_TERM_DEPOSIT)
		case MutualFunds:
			fiTypeList = append(fiTypeList, aa.FIType_FI_TYPE_MUTUAL_FUNDS)
		case Equities:
			fiTypeList = append(fiTypeList, aa.FIType_FI_TYPE_EQUITIES)
		case Nps:
			fiTypeList = append(fiTypeList, aa.FIType_FI_TYPE_NPS)
		case Etf:
			fiTypeList = append(fiTypeList, aa.FIType_FI_TYPE_ETF)
		case Reit:
			fiTypeList = append(fiTypeList, aa.FIType_FI_TYPE_REIT)
		case Invit:
			fiTypeList = append(fiTypeList, aa.FIType_FI_TYPE_INVIT)
		default:
		}
	}
	return fiTypeList
}

func consentTypesToString(consentTypes []aa.ConsentType) []string {
	var consentTypeList []string
	for _, cType := range consentTypes {
		switch cType {
		case aa.ConsentType_CONSENT_TYPE_PROFILE:
			consentTypeList = append(consentTypeList, Profile)
		case aa.ConsentType_CONSENT_TYPE_TRANSACTIONS:
			consentTypeList = append(consentTypeList, Transactions)
		case aa.ConsentType_CONSENT_TYPE_SUMMARY:
			consentTypeList = append(consentTypeList, Summary)
		default:
		}
	}
	return consentTypeList
}

func consentTypesToEnum(consentTypes []string) []aa.ConsentType {
	var consentTypeList []aa.ConsentType
	for _, cType := range consentTypes {
		switch cType {
		case Profile:
			consentTypeList = append(consentTypeList, aa.ConsentType_CONSENT_TYPE_PROFILE)
		case Transactions:
			consentTypeList = append(consentTypeList, aa.ConsentType_CONSENT_TYPE_TRANSACTIONS)
		case Summary:
			consentTypeList = append(consentTypeList, aa.ConsentType_CONSENT_TYPE_SUMMARY)
		default:
		}
	}
	return consentTypeList
}

func fetchTypeToString(fetchType aa.FetchType) string {
	switch fetchType {
	case aa.FetchType_FETCH_TYPE_ONETIME:
		return OneTime
	case aa.FetchType_FETCH_TYPE_PERIODIC:
		return Periodic
	default:
		return ""
	}
}

func fetchTypeToEnum(fetchType string) aa.FetchType {
	switch fetchType {
	case OneTime:
		return aa.FetchType_FETCH_TYPE_ONETIME
	case Periodic:
		return aa.FetchType_FETCH_TYPE_PERIODIC
	default:
		return aa.FetchType_FETCH_TYPE_UNSPECIFIED
	}
}

func consentModeToString(consentMode aa.ConsentMode) string {
	switch consentMode {
	case aa.ConsentMode_CONSENT_MODE_VIEW:
		return View
	case aa.ConsentMode_CONSENT_MODE_STORE:
		return Store
	case aa.ConsentMode_CONSENT_MODE_QUERY:
		return Query
	case aa.ConsentMode_CONSENT_MODE_STREAM:
		return Stream
	default:
		return ""
	}
}

func consentModeToEnum(consentMode string) aa.ConsentMode {
	switch consentMode {
	case View:
		return aa.ConsentMode_CONSENT_MODE_VIEW
	case Store:
		return aa.ConsentMode_CONSENT_MODE_STORE
	case Query:
		return aa.ConsentMode_CONSENT_MODE_QUERY
	case Stream:
		return aa.ConsentMode_CONSENT_MODE_STREAM
	default:
		return aa.ConsentMode_CONSENT_MODE_UNSPECIFIED
	}
}

func toOMConsentPurpose(purpose aa.ConsentPurpose) *onemoney.Purpose {
	switch purpose {
	case aa.ConsentPurpose_CONSENT_PURPOSE_WMS:
		return &onemoney.Purpose{
			Code:     "101",
			RefUri:   "https://api.rebit.org.in/aa/purpose/101.xml",
			Text:     "Wealth management service",
			Category: &onemoney.Category{Type: "string"},
		}
	default:
		return nil
	}
}

func toAAConsentPurpose(purpose *onemoney.Purpose) aa.ConsentPurpose {
	switch purpose.GetText() {
	case "Wealth management service":
		return aa.ConsentPurpose_CONSENT_PURPOSE_WMS
	default:
		return aa.ConsentPurpose_CONSENT_PURPOSE_UNSPECIFIED
	}
}

func toAAFIDataRange(fiDataRange *onemoney.FIDataRange) (*aa.FIDataRange, error) {
	fromTime, err1 := GetTimestampPbFromString(fiDataRange.GetFrom(), TimestampFormat)
	if err1 != nil {
		return nil, err1
	}
	toTime, err2 := GetTimestampPbFromString(fiDataRange.GetTo(), TimestampFormat)
	if err2 != nil {
		return nil, err2
	}
	return &aa.FIDataRange{
		From: fromTime,
		To:   toTime,
	}, nil
}

func toAAAccounts(accounts []*onemoney.Account) []*aa.Account {
	var accList []*aa.Account
	for _, acc := range accounts {
		ac := &aa.Account{
			FipId:           acc.GetFipId(),
			AccType:         acc.GetAccType(),
			LinkRefNumber:   acc.GetLinkRefNumber(),
			MaskedAccNumber: acc.GetMaskedAccNumber(),
		}
		switch acc.GetFiType() {
		case Deposit:
			ac.FiType = aa.FIType_FI_TYPE_DEPOSIT
		case TermDeposit1, TermDeposit2:
			ac.FiType = aa.FIType_FI_TYPE_TERM_DEPOSIT
		case RecurringDeposit:
			ac.FiType = aa.FIType_FI_TYPE_RECURRING_DEPOSIT
		case MutualFunds:
			ac.FiType = aa.FIType_FI_TYPE_MUTUAL_FUNDS
		case Nps:
			ac.FiType = aa.FIType_FI_TYPE_NPS
		case Equities:
			ac.FiType = aa.FIType_FI_TYPE_EQUITIES
		case Etf:
			ac.FiType = aa.FIType_FI_TYPE_ETF
		case Reit:
			ac.FiType = aa.FIType_FI_TYPE_REIT
		case Invit:
			ac.FiType = aa.FIType_FI_TYPE_INVIT
		default:
			ac.FiType = aa.FIType_FI_TYPE_UNSPECIFIED
		}
		accList = append(accList, ac)
	}
	return accList
}

func ToAAConsentDetails(cd *onemoney.ConsentDetail) (*aa.ConsentDetail, error) {
	consentStart, err1 := GetTimestampPbFromString(cd.GetConsentStart(), TimestampFormat)
	if err1 != nil {
		return nil, err1
	}
	consentExpiry, err2 := GetTimestampPbFromString(cd.GetConsentExpiry(), TimestampFormat)
	if err2 != nil {
		return nil, err2
	}
	fiDataRange, err3 := toAAFIDataRange(cd.GetFiDataRange())
	if err3 != nil {
		return nil, err3
	}
	return &aa.ConsentDetail{
		ConsentStart:  consentStart,
		ConsentExpiry: consentExpiry,
		ConsentMode:   consentModeToEnum(cd.GetConsentMode()),
		FetchType:     fetchTypeToEnum(cd.GetFetchType()),
		ConsentTypes:  consentTypesToEnum(cd.GetConsentTypes()),
		FiTypes:       toFiTypesEnum(cd.GetFiTypes()),
		DataConsumer: &aa.DataConsumer{
			Id:   cd.GetDataConsumer().GetId(),
			Type: cd.GetDataConsumer().GetType(),
		},
		DataProvider: &aa.DataProvider{
			Id:   cd.GetDataProvider().GetId(),
			Type: cd.GetDataProvider().GetType(),
		},
		Accounts:       toAAAccounts(cd.GetAccounts()),
		Customer:       &aa.Customer{Id: cd.GetCustomer().GetId()},
		ConsentPurpose: toAAConsentPurpose(cd.GetPurpose()),
		FiDataRange:    fiDataRange,
		DataLife:       toAADataLife(cd.GetDataLife()),
		Frequency:      toAAFrequency(cd.GetFrequency()),
		DataFilter:     toAADataFilter(cd.GetDataFilter()),
	}, nil
}

func toAAConsentStatus(cs string) aa.ConsentStatus {
	var aaCs aa.ConsentStatus
	switch cs {
	case "ACTIVE":
		aaCs = aa.ConsentStatus_CONSENT_STATUS_ACTIVE
	case "PAUSED":
		aaCs = aa.ConsentStatus_CONSENT_STATUS_PAUSED
	case "REVOKED":
		aaCs = aa.ConsentStatus_CONSENT_STATUS_REVOKED
	case "EXPIRED":
		aaCs = aa.ConsentStatus_CONSENT_STATUS_EXPIRED
	default:
		aaCs = aa.ConsentStatus_CONSENT_STATUS_UNSPECIFIED
	}
	return aaCs
}

func toCrypAlgo(crypAlgo aa.CryptoAlgo) string {
	switch crypAlgo {
	case aa.CryptoAlgo_CRYPTO_ALGO_ECDH:
		return "ECDH"
	default:
		return ""
	}
}

func toCrypCurve(crypCurve aa.CryptoCurve) string {
	switch crypCurve {
	case aa.CryptoCurve_CRYPTO_CURVE_25519:
		return "Curve25519"
	default:
		return ""
	}
}

func toCrypAlgoEnum(algo string) aa.CryptoAlgo {
	switch algo {
	case "ECDH":
		return aa.CryptoAlgo_CRYPTO_ALGO_ECDH
	default:
		return aa.CryptoAlgo_CRYPTO_ALGO_UNSPECIFIED
	}
}

func toCrypCurveEnum(curve string) aa.CryptoCurve {
	switch curve {
	case "Curve25519":
		return aa.CryptoCurve_CRYPTO_CURVE_25519
	default:
		return aa.CryptoCurve_CRYPTO_CURVE_UNSPECIFIED
	}
}

func toAAConsentHandleStatus(chs string) aa.ConsentHandleStatus {
	var aaChs aa.ConsentHandleStatus
	switch chs {
	// deprecated in V2
	case "READY":
		aaChs = aa.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY
	// deprecated in V2
	case "FAILED":
		aaChs = aa.ConsentHandleStatus_CONSENT_HANDLE_STATUS_FAILED
	case "PENDING":
		aaChs = aa.ConsentHandleStatus_CONSENT_HANDLE_STATUS_PENDING
	case "APPROVED":
		aaChs = aa.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY
	case "REJECTED":
		aaChs = aa.ConsentHandleStatus_CONSENT_HANDLE_STATUS_FAILED
	case "EXPIRED":
		aaChs = aa.ConsentHandleStatus_CONSENT_HANDLE_STATUS_FAILED
	default:
		aaChs = aa.ConsentHandleStatus_CONSENT_HANDLE_STATUS_UNSPECIFIED
	}
	return aaChs
}

func isValidApiVersion(ver string) bool {
	return ver == venAaPb.ApiVersionV1 || ver == venAaPb.ApiVersionV2
}

func GetApiVersion(apiVer venAaPb.RebitApiVersion) string {
	apiVersion, ok := venAaPb.RebitVersionEnumToString[apiVer]
	if !ok {
		logger.Warn("unspecified rebit api version received")
		return venAaPb.ApiVersionV1
	}
	return apiVersion
}
