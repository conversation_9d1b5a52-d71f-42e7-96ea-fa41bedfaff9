//nolint:gocritic
package dynamic_elements_getter

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/mohae/deepcopy"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/fieldmaskpb"

	"github.com/epifi/be-common/pkg/epifierrors"
	releasePkg "github.com/epifi/gamma/pkg/feature/release"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	dynamicElementsPb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/investment/dynamic_ui_element"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/usstocks"
	usStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	portfolioPb "github.com/epifi/gamma/api/usstocks/portfolio"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/usstocks/config/genconf"
)

type USSHomeDynamicElementsGetter struct {
	cfg                     *genconf.Config
	dynamicUiElementsClient dynamic_ui_element.DynamicUIElementServiceClient
	usStocksCatalogClient   usStocksCatalogPb.CatalogManagerClient
	usStocksPortfolioClient portfolioPb.PortfolioManagerClient
	releaseEvaluator        releasePkg.IEvaluator
}

func NewUSSHomeDynamicElementsGetter(
	cfg *genconf.Config,
	dynamicUiElementsClient dynamic_ui_element.DynamicUIElementServiceClient,
	usStocksCatalogClient usStocksCatalogPb.CatalogManagerClient,
	usStocksPortfolioClient portfolioPb.PortfolioManagerClient,
	releaseEvaluator releasePkg.IEvaluator) *USSHomeDynamicElementsGetter {
	return &USSHomeDynamicElementsGetter{
		cfg:                     cfg,
		dynamicUiElementsClient: dynamicUiElementsClient,
		usStocksCatalogClient:   usStocksCatalogClient,
		usStocksPortfolioClient: usStocksPortfolioClient,
		releaseEvaluator:        releaseEvaluator,
	}
}

// nolint: funlen
func (g *USSHomeDynamicElementsGetter) GetDynamicElements(ctx context.Context, actorId string, clientContext *dePb.ClientContext) ([]*dePb.DynamicElement, error) {
	var dynamicElements []*dynamicElementsPb.DynamicElement

	shouldAddTabbedCard, err := g.shouldAddTabbedCard(ctx, actorId, clientContext)
	if err != nil {
		return nil, errors.Wrap(err, "error in checking if tabbed card should be added or not")
	}

	if shouldAddTabbedCard {
		tabbedCard, tabbedCardErr := g.getHomeScreenTabbedCard(ctx, actorId, clientContext)
		if tabbedCardErr != nil {
			return nil, tabbedCardErr
		}

		if tabbedCard != nil {
			dynamicElements = append(dynamicElements, tabbedCard)
		}
	}

	switch clientContext.GetHomeInfo().GetSection() {
	case dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY:
		primaryWidgets, err2 := g.getThreePointWidget(ctx, actorId)
		if err2 != nil {
			logger.Error(ctx, "error in 3 point widget", zap.Error(err2))
			return nil, err2
		}
		dynamicElements = append(dynamicElements, primaryWidgets...)
	case dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY:
		secondaryWidgets, err2 := g.getThreeAndFourPointWidget(ctx, actorId)
		if err2 != nil {
			logger.Error(ctx, "error in 3 + 4 point widget", zap.Error(err2))
			return nil, err2
		}
		dynamicElements = append(dynamicElements, secondaryWidgets...)
	case dePb.HomeScreenAdditionalInfo_SECTION_BODY2:
		bodyWidgets, err2 := g.getHomeStocksCollection(ctx, actorId, clientContext)
		if err2 != nil {
			logger.Error(ctx, "error in HomeStocksCollection", zap.Error(err2))
			return nil, err2
		}
		dynamicElements = append(dynamicElements, bodyWidgets...)
	case dePb.HomeScreenAdditionalInfo_SECTION_TABBED_CARD:
		// do nothing, tabbed card is added already if allowed
	default:
		logger.Error(ctx, fmt.Sprintf("unhandled section in clientContext : %v", clientContext.GetHomeInfo().GetSection()))
		return nil, nil
	}
	return dynamicElements, nil
}

func (g *USSHomeDynamicElementsGetter) getHomeScreenTabbedCard(ctx context.Context, actorId string, clientContext *dePb.ClientContext) (*dynamicElementsPb.DynamicElement, error) {
	platform := clientContext.GetAppPlatform()
	appVersion := clientContext.GetAppVersion()

	if platform == commontypes.Platform_IOS && appVersion < int32(g.cfg.MinIOSVersionToSupportTabbedCard()) {
		logger.Debug(ctx, "IOS app version is below min supported version for tabbed card", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Int32(logger.APP_VERSION_CODE, appVersion), zap.Int("min_app_version_required", g.cfg.MinIOSVersionToSupportTabbedCard()))
		return nil, nil
	}

	if platform == commontypes.Platform_ANDROID && appVersion < int32(g.cfg.MinAndroidVersionToSupportTabbedCard()) {
		logger.Debug(ctx, "Android app version is below min supported version for tabbed card", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Int32(logger.APP_VERSION_CODE, appVersion), zap.Int("min_app_version_required", g.cfg.MinAndroidVersionToSupportTabbedCard()))
		return nil, nil
	}

	invDynamicUiElement, err := g.getHomeDynamicUIElementForUSSTabbedCard(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get home widget three point for usstock : %w", err)
	}

	if err = g.resolveCollectionAndPopulateStocksIfRequired(ctx, invDynamicUiElement, platform, appVersion, actorId); err != nil {
		return nil, errors.Wrap(err, "error in resolving and populating stocks for configured collection in tabbed card")
	}

	dynamicElement := getDynamicElement(invDynamicUiElement)

	if err = g.populateDynamicFields(ctx, actorId, dynamicElement); err != nil {
		return nil, errors.Wrapf(err, "error in populating dynamic fields to element")
	}

	return dynamicElement, nil
}

func getDynamicElement(invDynamicUiElement *dynamic_ui_element.DynamicUIElement) *dePb.DynamicElement {
	// If UssTabbedCardHomePage is configured in investment dynamic element wrapper object, returning the configured object
	if invDynamicUiElement.GetContentJson().GetUssTabbedCardHomePage().GetDynamicElement() != nil {
		return invDynamicUiElement.GetContentJson().GetUssTabbedCardHomePage().GetDynamicElement()
	}
	// To ensure backward compatibility, if uss tabbed card is NOT configured in the UssTabbedCardHomePage investment wrapper object, returning the object with previous implementation
	return invDynamicUiElement.GetContentJson().GetDynamicElement()
}

func (g *USSHomeDynamicElementsGetter) getThreePointWidget(ctx context.Context, actorId string) ([]*dePb.DynamicElement, error) {
	threePointsDynElem, err := g.getHomeDynamicUIElementByUseCase(ctx, actorId,
		dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_USSTOCKS_HOME_GTM_WIDGET_3_POINTS,
		dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS)

	if err != nil {
		return nil, fmt.Errorf("failed to get home widget three point for usstock : %w", err)
	}

	return []*dePb.DynamicElement{
		threePointsDynElem,
	}, nil
}

func (g *USSHomeDynamicElementsGetter) getThreeAndFourPointWidget(ctx context.Context, actorId string) ([]*dePb.DynamicElement, error) {
	var threePointsDynElem, fourPointsDynElem *dePb.DynamicElement

	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		var err error
		threePointsDynElem, err = g.getHomeDynamicUIElementByUseCase(gctx, actorId,
			dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_USSTOCKS_HOME_GTM_WIDGET_3_POINTS,
			dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS)

		if err != nil {
			return fmt.Errorf("failed to get home widget three point for usstock : %w", err)
		}
		return nil
	})
	grp.Go(func() error {
		var err error
		fourPointsDynElem, err = g.getHomeDynamicUIElementByUseCase(gctx, actorId,
			dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_USSTOCKS_HOME_GTM_WIDGET_4_POINTS,
			dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS)

		if err != nil {
			return fmt.Errorf("failed to get home widget four point for usstock : %w", err)
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, fmt.Errorf("failed to get 3 and 4 point widgets: %w", err)
	}

	return []*dePb.DynamicElement{
		threePointsDynElem,
		fourPointsDynElem,
	}, nil
}

func (g *USSHomeDynamicElementsGetter) getHomeDynamicUIElementByUseCase(ctx context.Context, actorId string, useCase dynamic_ui_element.DynamicUIUsecase,
	expectedStructureType dePb.ElementStructureType) (*dePb.DynamicElement, error) {
	dynUIElementResp, dynUIElementErr := g.dynamicUiElementsClient.EvaluateAndFetchDynamicUIElement(ctx, &dynamic_ui_element.EvaluateAndFetchDynamicUIElementRequest{
		Screen:  dynamic_ui_element.DynamicUIScreen_DYNAMIC_UI_SCREEN_HOME,
		Usecase: useCase,
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(dynUIElementResp, dynUIElementErr); rpcErr != nil {
		if dynUIElementResp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get dynamic ui element : %w", rpcErr)
	}
	dynElement := dynUIElementResp.GetDynamicUiElement().GetContentJson().GetDynamicElement()
	if dynElement.GetStructureType() != expectedStructureType {
		return nil, fmt.Errorf("expected %s dyn elemement, got %s", expectedStructureType.String(), dynElement.GetStructureType().String())
	}

	return dynElement, nil
}

func (g *USSHomeDynamicElementsGetter) populateDynamicFields(ctx context.Context, actorId string, dynElement *dynamicElementsPb.DynamicElement) error {
	// dynamic field is only populated for tabbed card at the moment, skipping if any other element encountered
	if dynElement.GetContent().GetTabbedCard() == nil {
		return nil
	}
	if err := g.fetchAndPopulateDailyPercentageReturns(ctx, dynElement); err != nil {
		return errors.Wrap(err, "error in populating RTSP initial price")
	}
	if err := g.fetchAndPopulatePortfolioValue(ctx, actorId, dynElement); err != nil {
		return errors.Wrap(err, "error in populating portfolio value")
	}

	// checking if the feature is enabled for the actor or not
	enabled, err := g.releaseEvaluator.Evaluate(ctx, releasePkg.NewCommonConstraintData(typesPb.Feature_FEATURE_USS_TABBED_CARD_RTSP).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in checking feature enablement for FEATURE_USS_TABBED_CARD_RTSP, disabled by default", zap.Error(err))
		// returning nil, as default behaviour should be RTSP failure rather than tabbed card failure
		turnOffRTSP(dynElement.GetContent().GetTabbedCard().GetTabs())
		return nil
	}

	if !enabled {
		logger.Debug(ctx, "RTSP not enabled for user as per feature release config", zap.String(logger.ACTOR_ID_V2, actorId))
		turnOffRTSP(dynElement.GetContent().GetTabbedCard().GetTabs())
	}
	return nil
}

// nolint:funlen
func (g *USSHomeDynamicElementsGetter) getHomeStocksCollection(ctx context.Context, actorId string, clientContext *dePb.ClientContext) ([]*dePb.DynamicElement, error) {
	if !g.cfg.HomeUsStocksCollection().IsHomeUsStocksCollectionReleased() {
		return nil, nil
	}

	res, err := g.usStocksCatalogClient.GetCollectionWithStocks(ctx, &usStocksCatalogPb.GetCollectionWithStocksRequest{
		CollectionId: g.cfg.HomeUsStocksCollection().CollectionId(),
		SortOption:   usStocksCatalogPb.SortOptionType_SORT_OPTION_TYPE_MARKET_CAP,
		PageContext: &rpcPb.PageContextRequest{
			PageSize: 5, // stocks list is shown on home. there is only space for 2 widgets hence getting 5 stocks is good enough
		},
		ActorId:  actorId,
		Platform: clientContext.GetAppPlatform(),
		Version:  uint32(clientContext.GetAppVersion()),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, fmt.Errorf("failed to get us stocks collection: %w", err)
	}
	var stocksElements []*dynamicElementsPb.BannerSingleShapeElement
	for _, collectionStock := range res.GetCollectionWithStocks().GetCollectionStocks() {
		stock := collectionStock.GetStock()
		stocksElements = append(stocksElements, &dynamicElementsPb.BannerSingleShapeElement{
			Shape:    dynamicElementsPb.BannerSingleShapeElement_SHAPE_STAMP_2,
			Image:    commontypes.GetImageFromUrl(stock.GetStockBasicDetails().GetLogoUrl()),
			Title:    commontypes.GetTextFromStringFontColourFontStyle(stock.GetStockBasicDetails().GetName().GetShortName(), colors.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
			BgColour: widget.GetBlockBackgroundColour(colors.ColorSnow),
			Shadow: []*widget.Shadow{
				{
					Height: 4,
					Colour: widget.GetBlockBackgroundColour(colors.ColorOnDarkMediumEmphasis),
				},
			},
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_USSTOCKS_SYMBOL_DETAILS_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_UsstocksSymbolDetailsScreenOptions{
					UsstocksSymbolDetailsScreenOptions: &deeplinkPb.USStocksSymbolDetailsScreenOptions{
						StockId:                             stock.GetId(),
						ShowUsstocksLandingOnBackNavigation: true,
					},
				},
			},
		})
	}
	return []*dePb.DynamicElement{
		{
			OwnerService:  typesPb.ServiceName_US_STOCKS_SERVICE,
			UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
			StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE,
			Content: &dynamicElementsPb.ElementContent{
				Content: &dynamicElementsPb.ElementContent_ScrollableBanner{
					ScrollableBanner: &dynamicElementsPb.ScrollableBannerElementContent{
						Header: &dynamicElementsPb.BannerHeader{
							Title: []*commontypes.Text{
								commontypes.GetTextFromStringFontColourFontStyle("Explore \nUS Stocks", colors.ColorOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_L),
							},
							Cta: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("See all", colors.ColorLightPrimaryAction, commontypes.FontStyle_BODY_XS),
								},
								RightIcon: &commontypes.Image{
									ImageUrl: "https://epifi-icons.pointz.in/rewards/fi-store-widget-green-right-arrow.png",
								},
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_USSTOCKS_LANDING_SCREEN,
								},
							},
						},
						ScrollingElements: stocksElements,
						BgColour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_RadialGradient{
								RadialGradient: &widget.RadialGradient{
									Colours: []string{"#EFF2F6"},
								},
							},
						},
					},
				},
			},
		},
	}, nil
}

func (g *USSHomeDynamicElementsGetter) fetchAndPopulateDailyPercentageReturns(ctx context.Context, element *dynamicElementsPb.DynamicElement) error {
	stockIds := getStockIds(element.GetContent().GetTabbedCard())
	// not proceeding ahead if no stock ids configured for RTSP
	if len(stockIds) == 0 {
		return nil
	}

	stocksResp, err := g.usStocksCatalogClient.GetStocks(ctx, &usStocksCatalogPb.GetStocksRequest{
		Identifiers: &usStocksCatalogPb.GetStocksRequest_StockIds{
			StockIds: &usStocksCatalogPb.RepeatedStrings{
				Ids: stockIds,
			},
		},
		FieldMask: &fieldmaskpb.FieldMask{
			Paths: []string{
				"symbol",
				"daily_performance",
				"exchange",
			},
		},
	})
	if err = epifigrpc.RPCError(stocksResp, err); err != nil {
		return errors.Wrap(err, "error in getting stocks")
	}

	populateDailyPercentageReturns(ctx, element.GetContent().GetTabbedCard(), stocksResp.GetStocks())

	return nil
}

// sets the value of RTSP flag to false for all the chips across all configured tabs
func turnOffRTSP(tabs []*dynamicElementsPb.TabbedCard_Tab) {
	for _, tab := range tabs {
		for _, chip := range tab.GetCard().GetChips() {
			chip.ShouldTrackRealtime = false
		}
	}
}

func (g *USSHomeDynamicElementsGetter) fetchAndPopulatePortfolioValue(ctx context.Context, actorId string, element *dynamicElementsPb.DynamicElement) error {
	// If collective info items is already populated, returning early
	// If collective info object is not null and list of items are not populated in config, proceeding to populate portfolio details
	if !lo.ContainsBy(element.GetContent().GetTabbedCard().GetTabs(), func(tab *dynamicElementsPb.TabbedCard_Tab) bool {
		// If collective info object is present and list of collective info items is not present
		return tab.GetCard().GetCollectiveInfo() != nil && len(tab.GetCard().GetCollectiveInfo().GetCollectiveInfos()) == 0
	}) {
		logger.Debug(ctx, "collective info object is nil, not populating portfolio values in collective info")
		return nil
	}

	portfolioDetails, err := g.getPortfolioDetails(ctx, actorId)
	if err != nil {
		return err
	}

	// if user does not have open positions, returning from here
	if portfolioDetails == nil || !portfolioDetails.hasOpenPositions {
		return nil
	}

	collectiveInfoItems := getCollectiveInfoItems(portfolioDetails)

	for _, tab := range element.GetContent().GetTabbedCard().GetTabs() {
		if tab.GetCard().GetCollectiveInfo() != nil &&
			len(tab.GetCard().GetCollectiveInfo().GetCollectiveInfos()) == 0 {
			tab.GetCard().GetCollectiveInfo().CollectiveInfos = collectiveInfoItems
		}
	}

	return nil
}

func getCollectiveInfoItems(portfolioDetails *PortfolioDetails) []*dynamicElementsPb.TabbedCard_Card_CollectiveInfo {
	return []*dynamicElementsPb.TabbedCard_Card_CollectiveInfo{
		{
			Primary: &ui.IconTextComponent{
				Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Portfolio ", "#6A6D70", commontypes.FontStyle_SUBTITLE_S),
					commontypes.GetTextFromStringFontColourFontStyle(moneyPkg.ToDisplayString(portfolioDetails.currentInvestmentValue), "#313234", commontypes.FontStyle_NUMBER_S)},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					Height:       20,
					CornerRadius: 12,
				},
			},
			Secondary: &ui.IconTextComponent{
				// nolint: verb
				Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%.2f%s", portfolioDetails.totalPlPercentage, "%"), "#648E4D", commontypes.FontStyle_NUMBER_S)},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					Height:       20,
					CornerRadius: 12,
				},
				LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: getArrowImgForPL(portfolioDetails.totalPlPercentage),
					},
					Properties: &commontypes.VisualElementProperties{
						Height: 20,
						Width:  20,
					},
					ImageType: commontypes.ImageType_PNG,
				}}},
			},
		},
		{
			Primary: &ui.IconTextComponent{
				Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Today", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					Height:       20,
					CornerRadius: 12,
				},
			},
			Secondary: &ui.IconTextComponent{
				Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%.2f%s", portfolioDetails.oneDayPlPercentage, "%"), "#A93D5B", commontypes.FontStyle_NUMBER_S)},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					Height:       20,
					CornerRadius: 12,
				},
				LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: getArrowImgForPL(portfolioDetails.oneDayPlPercentage),
					},
					Properties: &commontypes.VisualElementProperties{
						Height: 20,
						Width:  20,
					},
					ImageType: commontypes.ImageType_PNG,
				}}},
			},
		},
	}
}

type PortfolioDetails struct {
	totalPlPercentage, oneDayPlPercentage float64
	hasOpenPositions                      bool
	currentInvestmentValue                *moneyPb.Money
}

func (g *USSHomeDynamicElementsGetter) getPortfolioDetails(ctx context.Context, actorId string) (*PortfolioDetails, error) {
	var totalPlPercentageSum, oneDayPlPercentageSum, allPositionsCount float64
	currentInvestmentValue := moneyPkg.ZeroUSD()

	var pageCtx *rpcPb.PageContextRequest

	// Instead of infinite looping, having a hypothetically high iteration limit for getting open positions for user
	// Note: pagination is not implemented in GetAllOpenPositions RPC, below logic is to stay compatible after pagination is supported by the RPC
	for i := 0; i < 100; i++ {
		portfolioResp, err := g.usStocksPortfolioClient.GetAllOpenPositions(ctx, &portfolioPb.GetAllOpenPositionsRequest{
			PageContext: pageCtx,
			ActorId:     actorId,
			SortBy:      usstocks.PortfolioSortOptionType_PORTFOLIO_SORT_OPTION_TYPE_RETURNS_PERCENTAGE,
		})
		if err = epifigrpc.RPCError(portfolioResp, err); err != nil {
			return nil, errors.Wrap(err, "error in getting open positions for user")
		}

		for _, position := range portfolioResp.GetPositions() {
			totalPlPercentageSum += position.GetPlPercentage()
			oneDayPlPercentageSum += position.GetPlPercentageChangeToday()
			currentInvestmentValue, err = moneyPkg.Sum(currentInvestmentValue, position.GetMarketValue())
			if err != nil {
				return nil, errors.Wrap(err, "error in adding current investment value and position current market value")
			}
			allPositionsCount++
		}

		if !portfolioResp.GetPageContext().GetHasAfter() {
			break
		}

		pageCtx = &rpcPb.PageContextRequest{
			Token:    &rpcPb.PageContextRequest_AfterToken{AfterToken: portfolioResp.GetPageContext().GetAfterToken()},
			PageSize: 100,
		}
	}

	if allPositionsCount == 0 {
		logger.Debug(ctx, "no open position present for actor, not populating portfolio values in collective info")
		return &PortfolioDetails{hasOpenPositions: false}, nil
	}

	avgOverallPlPercentage := totalPlPercentageSum / allPositionsCount
	avgOneDayPlPercentage := oneDayPlPercentageSum / allPositionsCount

	return &PortfolioDetails{
		totalPlPercentage:      avgOverallPlPercentage,
		oneDayPlPercentage:     avgOneDayPlPercentage,
		hasOpenPositions:       true,
		currentInvestmentValue: currentInvestmentValue,
	}, nil
}

func (g *USSHomeDynamicElementsGetter) resolveCollectionAndPopulateStocksIfRequired(ctx context.Context, dynamicElement *dynamic_ui_element.DynamicUIElement,
	platform commontypes.Platform, appVersion int32, actorId string) error {
	collectionIds := dynamicElement.GetContentJson().GetUssTabbedCardHomePage().GetCollectionIds()
	maxAllowedChips := dynamicElement.GetContentJson().GetUssTabbedCardHomePage().GetMaxAllowedChipsInCard()
	// If collection Id is not configured, there is no collection to resolve to stocks, returning early
	if len(collectionIds) == 0 {
		return nil
	}

	tabs := dynamicElement.GetContentJson().GetUssTabbedCardHomePage().GetDynamicElement().GetContent().GetTabbedCard().GetTabs()
	// If tabs are not configured, returning early
	if len(tabs) == 0 {
		return nil
	}

	for idx := 0; idx < len(collectionIds) && idx < len(tabs); idx++ {
		collectionId := collectionIds[idx]
		tab := tabs[idx]
		if len(tab.GetCard().GetChips()) == 0 {
			logger.Info(ctx, "no chip found for tab to consider as template chip", zap.String(logger.COLLECTION_ID, collectionId))
			continue
		}
		templateChip := tab.GetCard().GetChips()[0]

		stocks, err := g.getStocksForCollection(ctx, collectionId, uint32(maxAllowedChips), platform, appVersion, actorId)
		if err != nil {
			return errors.Wrapf(err, "error in getting stocks for collection %s", collectionId)
		}

		// preparing a new copy of chips with stock related details, by using the template chip configured in every tab
		var chips []*dynamicElementsPb.TabbedCard_Card_Chip
		for i := 0; i < len(stocks) && i < int(maxAllowedChips); i++ {
			stock := stocks[i]
			c := deepcopy.Copy(templateChip).(*dynamicElementsPb.TabbedCard_Card_Chip)
			// updating stock specific details
			c.StockId = stock.GetId()
			c.Image.GetImage().Source = &commontypes.VisualElement_Image_Url{Url: stock.GetCompanyInfo().GetLogoUrl()}
			c.Title.DisplayValue = &commontypes.Text_PlainString{PlainString: stock.GetCompanyInfo().GetCompanyName().GetShortName()}
			c.Deeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_USSTOCKS_SYMBOL_DETAILS_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_UsstocksSymbolDetailsScreenOptions{
					UsstocksSymbolDetailsScreenOptions: &deeplinkPb.USStocksSymbolDetailsScreenOptions{StockId: stock.GetId()},
				},
			}
			chips = append(chips, c)
		}
		// replacing the existing chips list with the chips containing stocks for collection identified above
		tab.Card.Chips = chips
	}
	return nil
}

func (g *USSHomeDynamicElementsGetter) getStocksForCollection(ctx context.Context, collectionId string, maxRequestedStocks uint32,
	platform commontypes.Platform, appVersion int32, actorId string) ([]*usStocksCatalogPb.Stock, error) {
	collectionWithStocksResp, err := g.usStocksCatalogClient.GetCollectionWithStocks(ctx, &usStocksCatalogPb.GetCollectionWithStocksRequest{
		CollectionId:   collectionId,
		PageContext:    &rpcPb.PageContextRequest{PageSize: maxRequestedStocks},
		StockFieldMask: &fieldmaskpb.FieldMask{Paths: []string{"company_info"}},
		ActorId:        actorId,
		Platform:       platform,
		Version:        uint32(appVersion),
	})
	if err = epifigrpc.RPCError(collectionWithStocksResp, err); err != nil {
		return nil, errors.Wrapf(err, "error in getting collection with stock for collection %s", collectionId)
	}

	var stocks []*usStocksCatalogPb.Stock
	for _, collectionStock := range collectionWithStocksResp.GetCollectionWithStocks().GetCollectionStocks() {
		stocks = append(stocks, collectionStock.GetStock())
	}
	return stocks, nil
}

func (g *USSHomeDynamicElementsGetter) getHomeDynamicUIElementForUSSTabbedCard(ctx context.Context, actorId string) (*dynamic_ui_element.DynamicUIElement, error) {
	dynUIElementResp, dynUIElementErr := g.dynamicUiElementsClient.EvaluateAndFetchDynamicUIElement(ctx, &dynamic_ui_element.EvaluateAndFetchDynamicUIElementRequest{
		Screen:  dynamic_ui_element.DynamicUIScreen_DYNAMIC_UI_SCREEN_HOME,
		Usecase: dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_USSTOCKS_HOME_TABBED_CARD,
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(dynUIElementResp, dynUIElementErr); rpcErr != nil {
		if dynUIElementResp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get dynamic ui element : %w", rpcErr)
	}
	dynElement := getDynamicElement(dynUIElementResp.GetDynamicUiElement())
	if dynElement.GetStructureType() != dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_TABBED_CARD {
		return nil, fmt.Errorf("expected %s dyn elemement, got %s", dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_TABBED_CARD.String(),
			dynElement.GetStructureType().String())
	}

	return dynUIElementResp.GetDynamicUiElement(), nil
}

func (g *USSHomeDynamicElementsGetter) shouldAddTabbedCard(ctx context.Context, actorId string,
	clientContext *dePb.ClientContext) (bool, error) {

	// Below check is added to send tabbed card in SECTION_TABBED_CARD only,
	// as android client had an issue due to which UI was breaking in case of empty response for tabbed section dynamic element
	// more context: https://epifi.slack.com/archives/C063D6AHV1N/p1716881401216549
	// same was fixed in a subsequent app release, MinAndroidVersionWithSupportForEmptyTabbedSection defines the app version on which the bug was fixed
	if (clientContext.GetAppPlatform() == commontypes.Platform_ANDROID &&
		clientContext.GetAppVersion() < int32(g.cfg.MinAndroidVersionWithSupportForEmptyTabbedSection())) ||
		(clientContext.GetAppPlatform() == commontypes.Platform_IOS &&
			clientContext.GetAppVersion() < int32(g.cfg.MinIOSVersionWithSupportForEmptyTabbedSection())) {
		// allowing tabbed card for SECTION_TABBED_CARD only
		if clientContext.GetHomeInfo().GetSection() == dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_TABBED_CARD {
			return true, nil
		}
		return false, nil
	}

	// getting investment summary info for actor, to check if user has activated US Stocks
	invSummaryInfoResp, err := g.usStocksPortfolioClient.GetInvestmentSummaryInfo(ctx, &portfolioPb.GetInvestmentSummaryInfoRequest{
		ActorId:    actorId,
		FieldMasks: []portfolioPb.GetInvestmentSummaryInfoRequest_FieldMask{portfolioPb.GetInvestmentSummaryInfoRequest_FIELD_MASK_WALLET_SUMMARY},
	})
	if err = epifigrpc.RPCError(invSummaryInfoResp, err); err != nil {
		logger.Error(ctx, "error in getting investment summary info", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false, err
	}

	ussActivatedUser := invSummaryInfoResp.GetWalletSummaryInfo().GetSuccessOrdersCount() > 0

	switch clientContext.GetHomeInfo().GetSection() {
	case dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY, dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY:
		// not adding tabbed card in primary & secondary section, if user has activated us stocks feature
		if ussActivatedUser {
			return false, nil
		}
		return true, nil
	case dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_TABBED_CARD:
		// adding tabbed card in tabbed card section, only if user has activated us stocks feature
		if ussActivatedUser {
			return true, nil
		}
		return false, nil
	default:
		return false, nil
	}
}

func getArrowImgForPL(percentage float64) string {
	if percentage > 0 {
		return "https://epifi-icons.pointz.in/usstock_images/loss-small-img.png"
	}
	return "https://epifi-icons.pointz.in/usstock_images/growth-img-small.png"
}

func populateDailyPercentageReturns(ctx context.Context, card *dynamicElementsPb.TabbedCard, stocksMap map[string]*usStocksCatalogPb.Stock) {
	for _, tab := range card.GetTabs() {
		for _, stockChip := range tab.GetCard().GetChips() {
			if stock, present := stocksMap[stockChip.GetStockId()]; present {
				stockChip.Subtitle = GetStockPercentageReturns(stock.GetDailyPerformance().GetDailyPercentChange())
			} else {
				logger.Error(ctx, "required stock not present in stocks map, unable to populate initial value for stock",
					zap.String(logger.STOCK_ID, stockChip.GetStockId()))
				// Note: not propagating this error, as failure in populating initial value does not impact any functional flow
			}
		}
	}
}

func GetStockPercentageReturns(percentageChange float64) *ui.IconTextComponent {
	growthStr, textColor, iconUrl := GetPriceChangeTextColorIconURL(percentageChange)
	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  iconUrl,
			Width:     12,
			Height:    12,
		},
		Texts: []*commontypes.Text{
			usstocksUi.GetText(growthStr, textColor, commontypes.FontStyle_NUMBER_XS),
		},
		LeftImgTxtPadding: 2,
	}
}

func GetPriceChangeTextColorIconURL(percentageChange float64) (string, string, string) {
	textColor := usstocksUi.DarkMint
	iconUrl := "https://epifi-icons.pointz.in/usstocks_images/up-icon.png"
	if percentageChange < 0 {
		percentageChange = -percentageChange
		textColor = usstocksUi.DarkPeach
		iconUrl = "https://epifi-icons.pointz.in/usstocks_images/down-icon.png"
	}
	growthStr := fmt.Sprintf("%.2f%%", percentageChange)
	return growthStr, textColor, iconUrl
}

func getStockIds(card *dynamicElementsPb.TabbedCard) []string {
	var stockIds []string
	for _, tab := range card.GetTabs() {
		for _, chip := range tab.GetCard().GetChips() {
			stockIds = append(stockIds, chip.GetStockId())
		}
	}
	return stockIds
}
