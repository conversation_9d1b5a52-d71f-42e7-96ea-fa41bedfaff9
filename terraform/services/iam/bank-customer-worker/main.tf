terraform {
  backend "s3" {
    # Pass configuration from file through command line
    #profile = "demo"
  }
}

variable "service" {
  type    = string
  default = "bank-customer-worker"
}

data "aws_iam_policy" "s3-ssm-logging" {
  name = "ssm_s3_logging_policy"
}

module "bank-customer-worker" {
  source = "../../../modules/grpc-service/v3/iam"

  allow_clamav_policy_arn = var.allow_clamav_policy_arn
  env                     = var.env
  owner                   = var.owner
  service_name            = var.service
  k8_role                 = true
  eks_cluster_oidc_issuer = var.eks_cluster_oidc_issuer

  sqs_publisher = [
    "${var.env}-celestial-initiate-procrastinator-workflow-queue"
  ]

  secretmanagerread = [
    "${var.env}/cockroach/ca.crt",
    "${var.env}/cockroach/client.dev_user.*",
    "${var.env}/cockroach/client.epifi_dev_user.*",
    "${var.env}/gcloud/profiling-service-account-key",
    "${var.env}/cockroach/client.credit_card_dev_user.crt",
    "${var.env}/cockroach/client.credit_card_dev_user.key",
    "${var.env}/rudder/internal-writekey",
    "${var.env}/temporal/codec-encryption-key",
    "${var.env}/redis/common/fullaccess",
    "${var.env}/redis/user/prefixaccess",
  ]

  snstopics = ["${var.env}-celestial-workflow-update-topic"]

  s3readwrite    = flatten(var.s3readwrite_buckets)
  s3-ssm-logging = data.aws_iam_policy.s3-ssm-logging.arn

}
