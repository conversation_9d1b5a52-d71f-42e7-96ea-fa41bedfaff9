variable "env" { type = string }
variable "region" { type = string }
variable "owner" { type = string }

variable "iam_role_for_terraform" {
  description = "iam role for terraform"
  type        = string
  default     = ""
}

variable "aws_vpc" {
  description = "vpc_id"
  type        = string
}

variable "vpc_cidr" {
  description = "vpc_cidr"
  type        = list(string)
}

variable "instance_count" {
  description = "Number Of Instances"
  type        = number
  default     = 1
}

variable "instance_name" {
  type        = string
  default     = "Windows-server"
  description = "Name of the instance"

}
variable "tags" {
  type = map(any)
  default = {
    Name = "security-group"
  }
}

variable "bucket_name" {
  default = "demo-test-ec2-bucket"
}

variable "root_volume_size" {
  description = "The size, in GB, of the root EBS volume."
  type        = number
  default     = 20
}
