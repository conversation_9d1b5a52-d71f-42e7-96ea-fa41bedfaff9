env                    = "prod"
tenant                 = "stockguardian"
region                 = "ap-south-1"
resource_owner         = "jenkins+devops"
aws_vpc                = "vpc-040e598af04acc9be"
iam_role_for_terraform = "arn:aws:iam::329599620259:role/jenkins-iam-role"
owner                  = "329599620259"

vpc_cidr                = ["10.81.0.0/16"]
data_vpc_cidr           = ["10.60.0.0/16"]
ingress_main_vpc_cidr   = ["10.5.0.0/16"]
security_vpc_cidr       = ["10.30.0.0/16"]

private_vpc_id = "vpc-040e598af04acc9be"

private_subnets = ["subnet-0c605bfd9efd0e146", "subnet-0edf0482e67ad4d32"]
local_subnets   = ["subnet-050a4642d424ef506", "subnet-0ad9b51f9ab849fba"]
public_subnets  = ["subnet-021bab73c4f47433d", "subnet-038eb008ab3c495ce"]
pci_subnets     = ["subnet-050a4642d424ef506", "subnet-0ad9b51f9ab849fba"]

health_check_path           = "/_health"
pointz_zone_id              = "Z0223367Z1O2YLLVRW2Q"  # stockguardian.in - public
dns                         = "stockguardian.in"

alb_logging_bucket = "stockguardian-prod-alb"
pointz_certificate_arn          = "arn:aws:acm:ap-south-1:329599620259:certificate/ecee4845-93df-436f-9c50-2e24f40069ea"
env_wildcard_cert_arn           = "arn:aws:acm:ap-south-1:329599620259:certificate/ecee4845-93df-436f-9c50-2e24f40069ea"

allow_clamav_policy_arn = "arn:aws:iam::329599620259:policy/S3_clamav_read_write_access"

s3readwrite_buckets = ["stockguardian-audit-logs", "stockguardian-prod-non-pci-audit-logs"]

ingress_rules = {
  inbound_from_deploy = {
    from_port   = 0
    to_port     = -1
    protocol    = -1
    description = "Inbound from prod"
    cidr_blocks = ["*********/16"]
  }
}


egress_rules = {
  allow_all_traffic =  {
    from_port   = 0
    to_port     = -1
    protocol    = -1
    description = "Outbound to Internet"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
