package model

import (
	"time"

	"gorm.io/gorm"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
)

// ExchangerOfferActorAttempt stores each attempt to redeem an ExchangerOffer for a given actor
type ExchangerOfferActorAttempt struct {
	Id string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`

	// actor who attempted to redeem the offer
	ActorId string

	// exchanger offer for which redemption was attempted
	ExchangerOfferId string

	// unique requestId as sent by client for idempotency
	RequestId string

	// date on which redemption was attempted in IST
	AttemptedAtDate time.Time

	// standard timestamp fields
	CreatedAt time.Time
	UpdatedAt time.Time

	// soft delete the attempt entry if corresponding ExchangerOfferOrder fails
	DeletedAt gorm.DeletedAt
}

func NewExchangerOfferActorAttempt(exchangerOfferActorAttempt *exchangerPb.ExchangerOfferActorAttempt) *ExchangerOfferActorAttempt {
	return &ExchangerOfferActorAttempt{
		ActorId:          exchangerOfferActorAttempt.GetActorId(),
		ExchangerOfferId: exchangerOfferActorAttempt.GetExchangerOfferId(),
		RequestId:        exchangerOfferActorAttempt.GetRequestId(),
		AttemptedAtDate:  exchangerOfferActorAttempt.AttemptedAtDate.AsTime(),
	}
}

func (eoaa *ExchangerOfferActorAttempt) GetProto() *exchangerPb.ExchangerOfferActorAttempt {
	exchangeOfferActorAttempt := &exchangerPb.ExchangerOfferActorAttempt{
		Id:               eoaa.Id,
		ActorId:          eoaa.ActorId,
		ExchangerOfferId: eoaa.ExchangerOfferId,
		RequestId:        eoaa.RequestId,
		AttemptedAtDate:  timestampPb.New(eoaa.AttemptedAtDate),
		CreatedAt:        timestampPb.New(eoaa.CreatedAt),
		UpdatedAt:        timestampPb.New(eoaa.UpdatedAt),
	}

	if eoaa.DeletedAt.Valid {
		exchangeOfferActorAttempt.DeletedAt = timestampPb.New(eoaa.DeletedAt.Time)
	}

	return exchangeOfferActorAttempt
}
