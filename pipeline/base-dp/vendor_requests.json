{"database": "vendor_requests", "type": "vendor", "snowflake_schema_name": "VENDOR_REQUESTS", "bq_write": true, "org": "fi-wealth", "tables": {"vendor_requests": {"snowflake_database": "EPIFI_DATALAKE_WEALTH", "primary_key": "id", "table_name": "vendor_requests", "partition_col": "partition_column", "updated_at_key": "api", "transactional": true, "sf_task_incr_rw": true, "snowflake_merge": true, "snowflake_write_mode": "append", "bq_write_mode": "append", "table_size": "large", "base_etl": {}, "ds_etl": {}, "sf-dna_etl": {"dpvendormap": ["actorId:actor_id"]}}}}