{"name": "MyClass", "type": "record", "namespace": "com.acme.avro", "fields": [{"name": "livePerson", "type": "string"}, {"name": "videoIssues", "type": {"type": "array", "items": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"name": "userFaceShown", "type": "string"}, {"name": "reviewVerdict", "type": "string"}, {"name": "reviewedOn", "type": "string"}, {"name": "reviewedBy", "type": "string", "default": "NULL"}, {"name": "reReviewedBy", "default": "NULL", "type": "string"}, {"name": "reReviewedOn", "default": "NULL", "type": "string"}, {"name": "reReviewRemarks", "default": "NULL", "type": "string"}, {"name": "reReviewErrorType", "default": "NULL", "type": "string"}, {"name": "reReviewVerdict", "default": "NULL", "type": "string"}]}