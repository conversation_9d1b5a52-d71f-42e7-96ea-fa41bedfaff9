# Github Workflow Linter
Workflow linter verifies whether the workflows for servers/services include paths of all direct 
proto dependencies in it's path to trigger them when PRs are raised. This is added as part of a 
change which avoids triggering all workflows whenever a proto change is added in a PR. This is 
done to reduce the load on self-hosted GitHub runners by triggering workflows for closely related 
services.

For any workflow of a server, linter mandates proto paths of all services and vendorgateway
packages under all the services.


For any workflow of a service, linter mandates proto paths of all services from it's hosted server and vendorgateway
packages under all the services from the hosted server.

In addition, the linter also mandates common proto packages like api/types, api/rpc & api/queue. 
