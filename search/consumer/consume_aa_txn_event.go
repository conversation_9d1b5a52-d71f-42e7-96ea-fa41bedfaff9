package consumer

import (
	"context"
	"fmt"
	"strings"

	"github.com/golang/protobuf/ptypes"

	daoIndex "github.com/epifi/gamma/search/dao/index"

	"go.uber.org/zap"

	orderAaPb "github.com/epifi/gamma/api/order/aa"
	indexerPb "github.com/epifi/gamma/api/search/indexer"
	"github.com/epifi/be-common/pkg/logger"
)

func (consumer *IndexConsumerService) ProcessAATxnEvent(ctx context.Context, event *orderAaPb.AATxnUpdate) (*indexerPb.ProcessAaTxnEventResponse, error) {
	logger.Debug(ctx, "processing aa-txn event", zap.String("txn-id", event.GetAaTxn().GetId()))
	txnRecord, err := consumer.buildAaTxnRecord(ctx, event.GetAaTxn())
	if err != nil {
		logger.Error(ctx, "error in building aa-txn record", zap.Error(err))
		return &indexerPb.ProcessAaTxnEventResponse{ResponseHeader: genTransientFailure()}, fmt.Errorf("error in building aa-txn record: %w", err)
	}
	err = consumer.CurrIndexer.IndexAaTxnRecord(ctx, txnRecord)
	if err != nil {
		logger.Error(ctx, "failed to index aa txn event", zap.Error(err))
		// TODO(shubhra): check if message go to dead-letter queue
		if event.RequestHeader.IsLastAttempt {
			logger.Error(ctx, "last attempt to index transaction failed", zap.String("aa-txn-id", event.AaTxn.Id))
			return &indexerPb.ProcessAaTxnEventResponse{ResponseHeader: genTransientFailure()}, err
		}
		return &indexerPb.ProcessAaTxnEventResponse{ResponseHeader: genTransientFailure()}, err
	}
	return &indexerPb.ProcessAaTxnEventResponse{ResponseHeader: genSuccess()}, nil
}

//nolint:funlen
func (consumer *IndexConsumerService) buildAaTxnRecord(ctx context.Context, event *orderAaPb.Transaction) (*daoIndex.AATransaction, error) {
	aaTxn := event
	if aaTxn.GetId() == "" {
		return nil, fmt.Errorf("nil txn id: %s", aaTxn.GetId())
	}
	var (
		createdAtStr, updatedAtStr, deletedAtStr, executedAtStr, valueDateStr string
		fromActorName, toActorName                                            string
		txnPayload                                                            = aaTxn.GetPayload()
	)
	// return error in account-id is nil --> as txns are filtered in search with combination of <actor-id, account-id>
	if txnPayload.GetAccountId() == "" {
		return nil, fmt.Errorf("nil account id: %s", aaTxn.GetId())
	}
	executedAt, err := ptypes.Timestamp(aaTxn.GetExecutedAt())
	if err != nil {
		logger.Error(ctx, "error in timestamp executed at", zap.Error(err))
	} else {
		executedAtStr = executedAt.Format("2006-01-02 15:04:05")
	}
	valueDate, err := ptypes.Timestamp(txnPayload.GetValueDate())
	if err != nil {
		logger.Error(ctx, "error in timestamp valueDate", zap.Error(err))
	} else {
		valueDateStr = valueDate.Format("2006-01-02 15:04:05")
	}
	createdAt, err := ptypes.Timestamp(aaTxn.GetCreatedAt())
	if err != nil {
		logger.Error(ctx, "error in timestamp created-at", zap.Error(err))
	} else {
		createdAtStr = createdAt.Format("2006-01-02 15:04:05")
	}
	updatedAt, err := ptypes.Timestamp(aaTxn.GetUpdatedAt())
	if err != nil {
		logger.Error(ctx, "error in timestamp updated-at", zap.Error(err))
	} else {
		updatedAtStr = updatedAt.Format("2006-01-02 15:04:05")
	}
	if aaTxn.GetDeletedAt() != nil {
		deletedAt, err := ptypes.Timestamp(aaTxn.GetDeletedAt())
		if err != nil {
			logger.Error(ctx, "error in timestamp deleted-at", zap.Error(err))
		} else {
			deletedAtStr = deletedAt.Format("2006-01-02 15:04:05")
		}
	}
	if fromActor, err := consumer.getUserDetail(ctx, txnPayload.GetFromActorId()); err != nil {
		logger.Error(ctx, "error in getting from actor name", zap.Error(err))
	} else {
		fromActorName = fromActor.GetFullName()
	}
	if toActor, err := consumer.getUserDetail(ctx, txnPayload.GetToActorId()); err != nil {
		logger.Error(ctx, "error in getting to actor name", zap.Error(err))
	} else {
		toActorName = toActor.GetFullName()
	}
	txn := &daoIndex.AATransaction{
		Id:                  aaTxn.GetId(),
		PiFrom:              aaTxn.GetPiFrom(),
		PiTo:                aaTxn.GetPiTo(),
		Utr:                 aaTxn.GetUtr(),
		Amount:              aaTxn.GetAmount(),
		TransactionStatus:   "",
		PaymentProtocol:     []string{aaTxn.GetPaymentProtocol().String()},
		ExecutedAt:          executedAtStr,
		PartnerRefId:        aaTxn.GetPartnerRefId(),
		Remarks:             aaTxn.GetRemarks(),
		InternalTxnId:       aaTxn.GetAaTxnId(),
		TransactionCategory: txnPayload.GetCategory(),
		CurrentBalance:      txnPayload.GetCurrentBalance(),
		FromActorId:         txnPayload.GetFromActorId(),
		ToActorId:           txnPayload.GetToActorId(),
		FromActorFullName:   fromActorName,
		ToActorFullName:     toActorName,
		ValueDate:           valueDateStr,
		AccountId:           txnPayload.GetAccountId(),
		TransactionType:     aaTxn.GetTransactionType().String(),
		CreatedAt:           createdAtStr,
		UpdatedAt:           updatedAtStr,
		DeletedAt:           deletedAtStr,
	}

	// fill search related fields
	txn.SearchString = getSearchString(txn)
	// TODO(shubhra): fill `TxnCategoryCalculated` after integration with parser
	return txn, nil
}

func getSearchString(transaction *daoIndex.AATransaction) string {
	fieldsToBejoined := []string{transaction.TransactionType, transaction.TransactionMode, transaction.TransactionCategory, transaction.TxnCategoryCalculated,
		transaction.FromActorFullName, transaction.ToActorFullName}
	return strings.Join(fieldsToBejoined, ", ")
}
