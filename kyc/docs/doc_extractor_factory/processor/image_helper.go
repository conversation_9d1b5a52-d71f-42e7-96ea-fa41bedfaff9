package processor

import (
	"context"
	"encoding/base64"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/aws/v2/s3"

	img "github.com/epifi/gamma/pkg/image"
)

func PopulateImageBase64(ctx context.Context, s3Client s3.S3Client, image *commontypes.Image) error {
	imageS3Url, err := img.ExtractS3URL(image.GetImageUrl())
	if err != nil {
		return fmt.Errorf("error in extracting s3 url from image url: %w", err)
	}
	imageBytes, err := s3Client.Read(ctx, imageS3Url)
	if err != nil {
		return fmt.Errorf("error in reading image s3 url from image url: %w", err)
	}
	image.ImageDataBase64 = base64.StdEncoding.EncodeToString(imageBytes)
	return nil
}

// GetFileSizeFromBase64 decodes a base64 encoded string and returns the size of the decoded data in bytes.
// If the decoding fails, it returns -1.
//
// Parameters:
// - base64File: A string containing the base64 encoded data.
func GetFileSizeFromBase64(base64File string) int {
	decodedBytes, err := base64.StdEncoding.DecodeString(base64File)
	if err != nil {
		return -1
	}
	return len(decodedBytes)
}
