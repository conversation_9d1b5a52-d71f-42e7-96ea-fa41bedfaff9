groups:
  - name: slack-bot-service-group.rules
    rules:
      - alert: SlackBotInitFailed
        expr: sum(delta(service_group_initialisation_failures{server="nebula",service_group="slack_bot"}[5m])) by (environment,server,service_group) > 0
        for: 2m
        labels:
          severity: p0
          team: "platform"
        annotations:
          summary: "{{ $labels.service_group }} service group initialisation failed in {{ $labels.server }} server."
          description: "{{ $labels.service_group }} service group initialisation failed in {{ $labels.environment }} 2 minutes ago."
