groups:
  - name: frontend-webfe-api.rules
    rules:
      - alert: GetTravelDestinations
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetTravelDestinations", grpc_service="webfe.travel.TravelBudget"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GetTravelDestinations", grpc_service="webfe.travel.TravelBudget"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 1 and ON() sum(increase(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetTravelDestinations", grpc_service="webfe.travel.TravelBudget"}[15m])) > 1
        for: 5m
        labels:
          severity: "p0"
          team: central-growth
        annotations:
          summary: "webfe frontend API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "webfe frontend API {{ $labels.grpc_method }} has been down for more than 5m."

      - alert: GetTravelExpense
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetTravelExpense", grpc_service="webfe.travel.TravelBudget"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GetTravelExpense", grpc_service="webfe.travel.TravelBudget"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 1 and ON() sum(increase(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetTravelExpense", grpc_service="webfe.travel.TravelBudget"}[15m])) > 1
        for: 5m
        labels:
          severity: "p0"
          team: central-growth
        annotations:
          summary: "webfe frontend API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "webfe frontend API {{ $labels.grpc_method }} has been down for more than 5m."

      - alert: GetInternationalATMWithdrawalLimits
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetInternationalATMWithdrawalLimits", grpc_service="webfe.travel.TravelBudget"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GetInternationalATMWithdrawalLimits", grpc_service="webfe.travel.TravelBudget"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 1 and ON() sum(increase(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetInternationalATMWithdrawalLimits", grpc_service="webfe.travel.TravelBudget"}[15m])) > 1
        for: 5m
        labels:
          severity: "p0"
          team: central-growth
        annotations:
          summary: "webfe frontend API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "webfe frontend API {{ $labels.grpc_method }} has been down for more than 5m."

      - alert: GetForexExchangeRate
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetForexExchangeRate", grpc_service="webfe.travel.TravelBudget"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GetForexExchangeRate", grpc_service="webfe.travel.TravelBudget"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 1 and ON() sum(increase(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetForexExchangeRate", grpc_service="webfe.travel.TravelBudget"}[15m])) > 1
        for: 5m
        labels:
          severity: "p0"
          team: central-growth
        annotations:
          summary: "webfe frontend API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "webfe frontend API {{ $labels.grpc_method }} has been down for more than 5m."

      - alert: GetFiPromotionDetails
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetFiPromotionDetails", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GetFiPromotionDetails", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 10 and ON() sum(increase(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetFiPromotionDetails", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m])) > 1
        for: 5m
        labels:
          severity: "p2"
          team: lending
        annotations:
          summary: "webfe frontend API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "webfe frontend API {{ $labels.grpc_method }} has been down for more than 5m."

      - alert: GetLoansLandingPageDetails
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetLoansLandingPageDetails", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GetLoansLandingPageDetails", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 1 and ON() sum(increase(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetLoansLandingPageDetails", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m])) > 1
        for: 5m
        labels:
          severity: "p0"
          team: lending
        annotations:
          summary: "webfe frontend API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "webfe frontend API {{ $labels.grpc_method }} has been down for more than 5m."

      - alert: GetLoansNextEligibilityStep
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetLoansNextEligibilityStep", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="GetLoansNextEligibilityStep", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 1 and ON() sum(increase(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="GetLoansNextEligibilityStep", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m])) > 1
        for: 5m
        labels:
          severity: "p0"
          team: lending
        annotations:
          summary: "webfe frontend API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "webfe frontend API {{ $labels.grpc_method }} has been down for more than 5m."

      - alert: CollectUserDetails
        expr: sum(rate(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="CollectUserDetails", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_server_started_total{service="frontend",grpc_type="unary",grpc_method="CollectUserDetails", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 1 and ON() sum(increase(grpc_server_handled_total{service="frontend",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied",grpc_method="CollectUserDetails", grpc_service="webfe.loanseligibility.LoansEligibility"}[15m])) > 1
        for: 5m
        labels:
          severity: "p0"
          team: lending
        annotations:
          summary: "webfe frontend API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "webfe frontend API {{ $labels.grpc_method }} has been down for more than 5m."
