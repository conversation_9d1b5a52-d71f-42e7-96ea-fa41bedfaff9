groups:
  - name: vendorgateway-investment-analytics-api.rules
    rules:
      - alert: GetMFAnalyticsStaleData
        expr:  sum(rate(grpc_server_handled_total{service="vendorgateway",grpc_type="unary",grpc_code="Code(102)",grpc_method="GetMFAnalytics", grpc_service="vendorgateway.wealth.mutualfund.analytics.MFAnalytics"}[30m])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="vendorgateway",grpc_type="unary",grpc_method="GetMFAnalytics",grpc_service="vendorgateway.wealth.mutualfund.analytics.MFAnalytics"}[30m])) by (environment,grpc_service,grpc_method,service) * 100 > 40 and ON() sum(increase(grpc_server_handled_total{service="vendorgateway",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled",grpc_method="GetMFAnalytics", grpc_service="vendorgateway.wealth.mutualfund.analytics.MFAnalytics"}[30m])) > 5
        for: 15m
        labels:
          severity: "p1"
          team: "wealth"
        annotations:
          summary: "VG smallcase mf analytics API {{ $labels.grpc_method }} is returning stale data in {{ $labels.environment }}."
          description: "VG smallcase mf analytics API {{ $labels.grpc_method }} is returning stale data for more than 15m."
      - alert: GetMFAnalytics
        expr:  sum(rate(grpc_server_handled_total{service="vendorgateway",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|Code\\(102\\)",grpc_method="GetMFAnalytics", grpc_service="vendorgateway.wealth.mutualfund.analytics.MFAnalytics"}[1h])) by (environment,grpc_service,grpc_method,service)/sum(rate(grpc_server_started_total{service="vendorgateway",grpc_type="unary",grpc_method="GetMFAnalytics",grpc_service="vendorgateway.wealth.mutualfund.analytics.MFAnalytics"}[1h])) by (environment,grpc_service,grpc_method,service) * 100 > 2 and ON() sum(increase(grpc_server_handled_total{service="vendorgateway",grpc_type="unary",grpc_code!~"OK|Unauthenticated|PermissionDenied|Canceled|Code\\(102\\)",grpc_method="GetMFAnalytics", grpc_service="vendorgateway.wealth.mutualfund.analytics.MFAnalytics"}[1h])) > 5
        for: 15m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "VG smallcase mf analytics API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "VG smallcase mf analytics API {{ $labels.grpc_method }} is down for more than 15m."


