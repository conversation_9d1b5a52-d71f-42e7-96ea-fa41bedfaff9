groups:
  - name: infra_argo_rules
    rules:
      - alert: "ArgoRolloutControllerDown"
        expr: >-
          kube_replicaset_status_ready_replicas{
            replicaset=~"argo-rollouts-.*", 
            replicaset!~"argo-rollouts-dashboard-.*"
          } == 0
        for: 5m
        labels:
          severity: P0
          service: "argo-rollout"
          team: "ied"
        annotations:
          summary: "No Argo Rollout controllers available since last 5m"
      - alert: "ArgoRolloutDashboardDown"
        expr: >-
          kube_replicaset_status_ready_replicas{
            replicaset=~"argo-rollouts-dashboard-.*"
          } == 0
        for: 5m
        labels:
          severity: P0
          service: "argo-rollout"
          team: "ied"
        annotations:
          summary: "Argo Rollout dashboard down since last 5m"
