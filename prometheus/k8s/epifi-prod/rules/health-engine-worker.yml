groups:
  - name: health-engine-worker.rules
    rules:
      - alert: HealthEngineWorkflowFailure
        expr: ((sum by(exported_namespace) (increase(workflow_failed{operation="CompletionStats",type="history", exported_namespace="prod_health_engine"}[1h])) OR ON() vector(0))) > 0
        for: 1m
        labels:
          severity: "p0"
          team: "pay"
          service: "health-engine-worker"
        annotations:
          summary: "Failure in health-engine workflow"
          description: "Workflow failed in health-engine namespace."
      - alert: CsisStatusCheckWorkflowActivityFailure
        # The following alert will help in knowing when the activity which checks for CSIS status runs into transient errors for more than 450 times in last 12hrs.
        # The figure '450' is chosen as a safe bet based on past 100d stats that, if the retries are exceeding this number, it's likely that the activity will run its full course of
        # retry-attempt and eventually fail.
        # Thus, this alert will help us in knowing a potential failure of the whole workflow so that we can schedule it again.
        expr: sum(increase(temporal_activity_failures_total{activity_type="PollCsisStatus",workflow_type="CsisStatusCheck", error_type="TransientError"}[12h])) > 450
        for: 15m
        labels:
          severity: "p0"
          team: "pay"
          service: "health-engine-worker"
        annotations:
          summary: "PollCsisStatus (CSIS status check) Activity transient-failure threshold breached"
          description: "PollCsisStatus (CSIS status check) Activity transient-failure threshold breached"
