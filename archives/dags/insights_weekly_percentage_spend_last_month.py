import sys
sys.path.append('/home/<USER>/airflow')
from airflow.utils.dates import days_ago
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from scripts.epiview.execute_epiview import execute_epiview


default_args = {
    'owner': '<EMAIL>',
    'start_date': days_ago(1),
    'depends_on_past': False
}

dag_name = 'insights_weekly_percentage_spend_last_month'

dag = DAG(
    dag_name,
    default_args=default_args,
    schedule_interval='0 21 4 * *',
    catchup=False,
    max_active_runs=1
)

execute_merchant_spends_mom_comparison = PythonOperator(
    task_id='execute_insights_weekly_percentage_spend_last_month',
    python_callable=execute_epiview,
    op_kwargs={'jobMode': 'script', 'config_name': 'insights_weekly_percentage_spend_last_month', 'spark_config_id': 'entity_config', 'dag_name': dag_name},
    provide_context=True,
    dag=dag
)
